<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>管理后台 - 订单管理系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background-color: #f5f5f5;
            color: #333;
        }

        /* 顶部导航栏 */
        .header {
            background: #001529;
            color: white;
            height: 64px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 24px;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1000;
        }

        .logo {
            display: flex;
            align-items: center;
            font-size: 20px;
            font-weight: bold;
        }

        .logo::before {
            content: "🏢";
            margin-right: 8px;
        }

        .header-right {
            display: flex;
            align-items: center;
            gap: 16px;
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 8px;
            color: #fff;
        }

        /* 侧边导航栏 */
        .sidebar {
            position: fixed;
            top: 64px;
            left: 0;
            width: 200px;
            height: calc(100vh - 64px);
            background: #001529;
            overflow-y: auto;
            z-index: 100;
        }

        .menu-item {
            display: flex;
            align-items: center;
            padding: 16px 24px;
            color: rgba(255, 255, 255, 0.65);
            cursor: pointer;
            transition: all 0.3s;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .menu-item:hover {
            background: rgba(255, 255, 255, 0.1);
            color: #fff;
        }

        .menu-item.active {
            background: #1890ff;
            color: #fff;
        }

        .menu-item-icon {
            margin-right: 12px;
            font-size: 16px;
        }

        /* 二级菜单样式 */
        .menu-item.has-submenu {
            position: relative;
        }

        .menu-item.has-submenu::after {
            content: "▶";
            position: absolute;
            right: 24px;
            transition: transform 0.3s;
            font-size: 12px;
        }

        .menu-item.has-submenu.expanded::after {
            transform: rotate(90deg);
        }

        .submenu {
            background: rgba(0, 0, 0, 0.3);
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease;
        }

        .menu-item.has-submenu.expanded .submenu {
            max-height: 200px;
        }

        .submenu.expanded {
            max-height: 200px;
        }

        .submenu-item {
            display: flex;
            align-items: center;
            padding: 12px 24px 12px 48px;
            color: rgba(255, 255, 255, 0.65);
            cursor: pointer;
            transition: all 0.3s;
            border-bottom: 1px solid rgba(255, 255, 255, 0.05);
        }

        .submenu-item:hover {
            background: rgba(255, 255, 255, 0.1);
            color: #fff;
        }

        .submenu-item.active {
            background: #1890ff;
            color: #fff;
        }

        .submenu-item-icon {
            margin-right: 8px;
            font-size: 14px;
        }

        /* 主内容区域 */
        .main-content {
            margin-left: 200px;
            margin-top: 64px;
            padding: 24px;
            min-height: calc(100vh - 64px);
        }

        .page {
            display: none;
        }

        .page.active {
            display: block;
        }

        /* 卡片样式 */
        .card {
            background: white;
            border-radius: 8px;
            padding: 24px;
            margin-bottom: 24px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .card-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 16px;
            color: #262626;
        }

        /* 统计卡片 */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 24px;
            margin-bottom: 24px;
        }

        .stat-card {
            background: white;
            border-radius: 8px;
            padding: 24px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            text-align: center;
        }

        .stat-number {
            font-size: 32px;
            font-weight: bold;
            color: #1890ff;
            margin-bottom: 8px;
        }

        .stat-label {
            color: #666;
            font-size: 14px;
        }

        /* 表格样式 */
        .table {
            width: 100%;
            border-collapse: collapse;
            background: white;
        }

        .table th,
        .table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #f0f0f0;
        }

        .table th {
            background: #fafafa;
            font-weight: 600;
            color: #262626;
        }

        .table tr:hover {
            background: #f5f5f5;
        }

        /* 按钮样式 */
        .btn {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            padding: 8px 16px;
            border: 1px solid #d9d9d9;
            border-radius: 6px;
            background: white;
            color: #333;
            text-decoration: none;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s;
            margin-right: 8px;
        }

        .btn:hover {
            border-color: #1890ff;
            color: #1890ff;
        }

        .btn-primary {
            background: #1890ff;
            border-color: #1890ff;
            color: white;
        }

        .btn-primary:hover {
            background: #40a9ff;
            border-color: #40a9ff;
            color: white;
        }

        .btn-danger {
            background: #ff4d4f;
            border-color: #ff4d4f;
            color: white;
        }

        .btn-danger:hover {
            background: #ff7875;
            border-color: #ff7875;
            color: white;
        }

        .btn-warning {
            background: #faad14;
            border-color: #faad14;
            color: white;
        }

        .btn-warning:hover {
            background: #ffc53d;
            border-color: #ffc53d;
            color: white;
        }

        /* 状态标签 */
        .status-tag {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
        }

        .status-pending {
            background: #fff7e6;
            color: #fa8c16;
            border: 1px solid #ffd591;
        }

        .status-paid {
            background: #f6ffed;
            color: #52c41a;
            border: 1px solid #b7eb8f;
        }

        .status-cancelled {
            background: #fff2f0;
            color: #ff4d4f;
            border: 1px solid #ffccc7;
        }

        .status-completed {
            background: #e6f7ff;
            color: #1890ff;
            border: 1px solid #91d5ff;
        }

        /* 订单类型标签 */
        .order-type-tag {
            padding: 3px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
            display: inline-block;
        }

        .order-type-新购 {
            background: #f6ffed;
            color: #52c41a;
            border: 1px solid #b7eb8f;
        }

        .order-type-续费 {
            background: #e6f7ff;
            color: #1890ff;
            border: 1px solid #91d5ff;
        }

        .order-type-变更 {
            background: #fff7e6;
            color: #fa8c16;
            border: 1px solid #ffd591;
        }

        /* 资源状态标签 */
        .status-creating {
            background: #e6f7ff;
            color: #1890ff;
            border: 1px solid #91d5ff;
        }

        /* 搜索和筛选区域 */
        .filter-area {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 16px;
            flex-wrap: wrap;
        }

        .filter-area input,
        .filter-area select {
            padding: 8px 12px;
            border: 1px solid #d9d9d9;
            border-radius: 6px;
            font-size: 14px;
        }

        .filter-area input {
            width: 200px;
        }

        /* 分页样式 */
        .pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 8px;
            margin-top: 24px;
        }

        .pagination-btn {
            padding: 8px 12px;
            border: 1px solid #d9d9d9;
            background: white;
            color: #333;
            cursor: pointer;
            border-radius: 4px;
            transition: all 0.3s;
        }

        .pagination-btn:hover {
            border-color: #1890ff;
            color: #1890ff;
        }

        .pagination-btn.active {
            background: #1890ff;
            border-color: #1890ff;
            color: white;
        }

        .pagination-btn:disabled {
            background: #f5f5f5;
            color: #ccc;
            cursor: not-allowed;
        }

        /* 模态框样式 */
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 2000;
            justify-content: center;
            align-items: center;
        }

        .modal-content {
            background: white;
            border-radius: 8px;
            padding: 24px;
            max-width: 500px;
            width: 90%;
            max-height: 80vh;
            overflow-y: auto;
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
            padding-bottom: 16px;
            border-bottom: 1px solid #f0f0f0;
        }

        .modal-title {
            font-size: 18px;
            font-weight: 600;
        }

        .modal-close {
            background: none;
            border: none;
            font-size: 24px;
            cursor: pointer;
            color: #999;
        }

        .modal-close:hover {
            color: #333;
        }

        .modal-footer {
            display: flex;
            justify-content: flex-end;
            gap: 12px;
            margin-top: 24px;
            padding-top: 16px;
            border-top: 1px solid #f0f0f0;
        }

        /* 表单样式 */
        .form-group {
            margin-bottom: 16px;
        }

        .form-label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #333;
        }

        .form-input {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #d9d9d9;
            border-radius: 6px;
            font-size: 14px;
        }

        .form-input:focus {
            outline: none;
            border-color: #1890ff;
            box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
        }

        /* 状态标签样式 */
        .status-badge {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
        }

        .status-active {
            background: #f6ffed;
            color: #52c41a;
            border: 1px solid #b7eb8f;
        }

        .status-inactive {
            background: #fff2e8;
            color: #fa8c16;
            border: 1px solid #ffd591;
        }

        /* 操作按钮样式 */
        .action-buttons {
            display: flex;
            gap: 8px;
            flex-wrap: wrap;
        }

        .btn-link {
            background: none;
            border: none;
            color: #1890ff;
            cursor: pointer;
            font-size: 12px;
            padding: 4px 8px;
            border-radius: 4px;
            transition: all 0.3s;
        }

        .btn-link:hover {
            background: #e6f7ff;
        }

        .btn-link.danger {
            color: #ff4d4f;
        }

        .btn-link.danger:hover {
            background: #fff2f0;
        }

        /* 表格容器样式 */
        .table-container {
            margin: 20px 0;
            border-radius: 6px;
            overflow-x: auto;
            border: 1px solid #f0f0f0;
            background: white;
        }

        /* 数据表格样式优化 */
        .data-table {
            width: 100%;
            min-width: 1400px;
            border-collapse: collapse;
            font-size: 13px;
        }

        .data-table th {
            background: #fafafa;
            padding: 16px 12px;
            text-align: left;
            font-weight: 600;
            color: #333;
            border-bottom: 2px solid #f0f0f0;
            white-space: nowrap;
        }

        .data-table td {
            padding: 16px 12px;
            border-bottom: 1px solid #f5f5f5;
            vertical-align: top;
            line-height: 1.4;
        }

        .data-table tbody tr:hover {
            background: #f9f9f9;
        }

        .data-table tbody tr:last-child td {
            border-bottom: none;
        }

        /* 分页样式 */
        .pagination {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 16px;
            padding: 12px 0;
        }

        .pagination-info {
            color: #666;
            font-size: 14px;
        }

        .pagination-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .pagination-btn {
            padding: 6px 12px;
            border: 1px solid #d9d9d9;
            background: white;
            cursor: pointer;
            border-radius: 4px;
            font-size: 14px;
        }

        .pagination-btn:hover:not(:disabled) {
            border-color: #1890ff;
            color: #1890ff;
        }

        .pagination-btn:disabled {
            background: #f5f5f5;
            color: #999;
            cursor: not-allowed;
        }

        .pagination-current {
            padding: 6px 12px;
            background: #1890ff;
            color: white;
            border-radius: 4px;
            font-size: 14px;
        }

        /* 表单网格样式 */
        .form-grid {
            display: grid;
            gap: 20px;
        }

        .form-group {
            margin-bottom: 16px;
        }

        .form-group label {
            display: block;
            margin-bottom: 6px;
            font-weight: 500;
            color: #333;
        }

        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            font-size: 14px;
        }

        .form-group input:focus,
        .form-group select:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: #1890ff;
            box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
        }

        /* 多选配置区域样式 */
        .multi-select-container {
            border: 1px solid #d9d9d9;
            border-radius: 6px;
            background: #fafafa;
            padding: 16px;
            max-height: 220px;
            overflow-y: auto;
        }

        .multi-select-container:focus-within {
            border-color: #1890ff;
            box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.1);
        }

        .multi-select-title {
            font-weight: 600;
            color: #333;
            margin-bottom: 12px;
            font-size: 13px;
            display: flex;
            align-items: center;
        }

        .multi-select-title::before {
            content: "📍";
            margin-right: 6px;
        }

        .checkbox-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
            gap: 8px;
        }

        .checkbox-item {
            display: flex;
            align-items: center;
            padding: 8px 12px;
            background: white;
            border: 1px solid #e8e8e8;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.2s;
            font-size: 13px;
        }

        .checkbox-item:hover {
            border-color: #1890ff;
            background: #f0f8ff;
        }

        .checkbox-item input[type="checkbox"] {
            margin-right: 8px;
            accent-color: #1890ff;
        }

        .checkbox-item input[type="checkbox"]:checked + span {
            color: #1890ff;
            font-weight: 500;
        }

        .empty-state {
            text-align: center;
            color: #999;
            font-style: italic;
            padding: 20px;
            background: white;
            border: 1px dashed #d9d9d9;
            border-radius: 4px;
        }

        .empty-state::before {
            content: "🔍";
            display: block;
            font-size: 24px;
            margin-bottom: 8px;
        }
    </style>
</head>
<body>
    <!-- 顶部导航栏 -->
    <header class="header">
        <div class="logo">管理后台</div>
        <div class="header-right">
            <div class="user-info">
                <span>👤</span>
                <span>管理员</span>
            </div>
        </div>
    </header>

    <!-- 侧边导航栏 -->
    <nav class="sidebar">
        <div class="menu-item active" data-page="orders">
            <span class="menu-item-icon">📋</span>
            <span>订单管理</span>
        </div>
        <div class="menu-item has-submenu">
            <span class="menu-item-icon">💻</span>
            <span>产品管理</span>
        </div>
        <div class="submenu" id="products-submenu">
            <div class="submenu-item" data-page="cloud-servers">
                <span class="submenu-item-icon">🖥️</span>
                <span>计算</span>
            </div>
            <div class="submenu-item" data-page="cloud-disks">
                <span class="submenu-item-icon">💾</span>
                <span>云硬盘</span>
            </div>
        </div>
        <div class="menu-item" data-page="customers">
            <span class="menu-item-icon">👥</span>
            <span>客户管理</span>
        </div>
        <div class="menu-item" data-page="reports">
            <span class="menu-item-icon">📊</span>
            <span>报表统计</span>
        </div>
    </nav>

    <!-- 主内容区域 -->
    <main class="main-content">
        <!-- 订单管理页面 -->
        <div class="page active" id="orders">
            <!-- 统计概览 -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-number">156</div>
                    <div class="stat-label">总订单数</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">23</div>
                    <div class="stat-label">待付款订单</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">¥128,560</div>
                    <div class="stat-label">本月销售额</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">89</div>
                    <div class="stat-label">已完成订单</div>
                </div>
            </div>

            <div class="card">
                <div class="card-title">订单列表</div>
                
                <!-- 搜索和筛选区域 -->
                <div class="filter-area">
                    <input type="text" placeholder="搜索订单号、客户名称..." id="searchInput">
                    <select id="statusFilter">
                        <option value="">全部状态</option>
                        <option value="pending">待付款</option>
                        <option value="paid">已付款</option>
                        <option value="completed">已完成</option>
                        <option value="cancelled">已取消</option>
                    </select>
                    <select id="productFilter">
                        <option value="">全部产品</option>
                        <option value="ecs">云服务器ECS</option>
                        <option value="rds">云数据库RDS</option>
                        <option value="oss">对象存储OSS</option>
                        <option value="cdn">CDN加速</option>
                    </select>
                    <button class="btn btn-primary" onclick="searchOrders()">搜索</button>
                    <button class="btn" onclick="resetFilters()">重置</button>
                </div>

                <!-- 订单表格 -->
                <table class="table">
                    <thead>
                        <tr>
                            <th>订单号</th>
                            <th>客户名称</th>
                            <th>类型</th>
                            <th>产品名称</th>
                            <th>SKU编号</th>
                            <th>订单金额</th>
                            <th>订单状态</th>
                            <th>创建时间</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody id="ordersTableBody">
                        <!-- 订单数据将通过JavaScript动态填充 -->
                    </tbody>
                </table>

                <!-- 分页 -->
                <div class="pagination">
                    <button class="pagination-btn" onclick="changePage(-1)">上一页</button>
                    <button class="pagination-btn active">1</button>
                    <button class="pagination-btn">2</button>
                    <button class="pagination-btn">3</button>
                    <button class="pagination-btn" onclick="changePage(1)">下一页</button>
                </div>
            </div>
        </div>

        <!-- 订单详情页面 -->
        <div class="page" id="order-detail">
            <!-- 返回按钮 -->
            <div style="margin-bottom: 20px;">
                <button class="btn" onclick="goBackToOrderList()" style="display: flex; align-items: center; gap: 8px;">
                    <span>←</span>
                    <span>返回订单列表</span>
                </button>
            </div>

            <!-- 订单基本信息 -->
            <div class="card">
                <div class="card-title">订单基本信息</div>
                <div style="display: grid; grid-template-columns: repeat(4, 1fr); gap: 20px; margin-top: 20px;">
                    <div>
                        <label style="font-weight: 500; color: #666; font-size: 14px;">订单号</label>
                        <div id="detailOrderId" style="margin-top: 4px; font-size: 16px; font-weight: 600; color: #1890ff;"></div>
                    </div>
                    <div>
                        <label style="font-weight: 500; color: #666; font-size: 14px;">客户名称</label>
                        <div id="detailCustomerName" style="margin-top: 4px; font-size: 16px;"></div>
                    </div>
                    <div>
                        <label style="font-weight: 500; color: #666; font-size: 14px;">订单类型</label>
                        <div id="detailOrderType" style="margin-top: 4px;"></div>
                    </div>
                    <div>
                        <label style="font-weight: 500; color: #666; font-size: 14px;">订单状态</label>
                        <div id="detailOrderStatus" style="margin-top: 4px;"></div>
                    </div>
                    <div>
                        <label style="font-weight: 500; color: #666; font-size: 14px;">订单金额</label>
                        <div id="detailOrderAmount" style="margin-top: 4px; font-size: 16px; font-weight: 600; color: #f5222d;"></div>
                    </div>
                    <div>
                        <label style="font-weight: 500; color: #666; font-size: 14px;">创建时间</label>
                        <div id="detailCreateTime" style="margin-top: 4px;"></div>
                    </div>
                    <div>
                        <label style="font-weight: 500; color: #666; font-size: 14px;">付款时间</label>
                        <div id="detailPayTime" style="margin-top: 4px;"></div>
                    </div>
                    <div>
                        <label style="font-weight: 500; color: #666; font-size: 14px;">客户邮箱</label>
                        <div id="detailCustomerEmail" style="margin-top: 4px;"></div>
                    </div>
                </div>
                <div style="margin-top: 20px;">
                    <label style="font-weight: 500; color: #666; font-size: 14px;">备注信息</label>
                    <div id="detailNotes" style="margin-top: 4px; padding: 12px; background: #f5f5f5; border-radius: 4px; min-height: 40px;"></div>
                </div>
            </div>

            <!-- 子订单列表 -->
            <div class="card">
                <div class="card-title">子订单列表</div>
                <table class="table" style="margin-top: 20px;">
                    <thead>
                        <tr>
                            <th style="width: 200px;">产品名称</th>
                            <th style="width: 150px;">SKU信息</th>
                            <th style="width: 100px;">计费方式</th>
                            <th style="width: 80px;">时长</th>
                            <th style="width: 60px;">数量</th>
                            <th style="width: 100px;">金额</th>
                            <th style="width: 100px;">资源状态</th>
                        </tr>
                    </thead>
                    <tbody id="orderDetailSkuTable">
                        <!-- 子订单数据将通过JavaScript动态填充 -->
                    </tbody>
                </table>
            </div>
        </div>

        <!-- 产品详情页 -->
<div class="page" id="product-detail">
    <div class="card">
        <div class="card-title" style="display: flex; justify-content: space-between; align-items: center;">
            <span id="productDetailTitle">产品详情</span>
            <button class="btn" onclick="goBackToProductList()">返回列表</button>
        </div>
        
        <!-- 产品基本信息 -->
        <div style="margin-bottom: 30px;">
            <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 20px; margin-bottom: 20px;">
                <div>
                    <label style="color: #666; font-size: 14px;">产品名称</label>
                    <div id="detailProductName" style="font-weight: 600;"></div>
                </div>
                <div>
                    <label style="color: #666; font-size: 14px;">套餐规格</label>
                    <div id="detailPackageSpec" style="font-weight: 600;"></div>
                </div>
                <div>
                    <label style="color: #666; font-size: 14px;">架构</label>
                    <div id="detailArchitecture"></div>
                </div>
                <div>
                    <label style="color: #666; font-size: 14px;">规格类型</label>
                    <div id="detailSpecType"></div>
                </div>
                <div>
                    <label style="color: #666; font-size: 14px;">CPU配置</label>
                    <div id="detailCpuConfig"></div>
                </div>
                <div>
                    <label style="color: #666; font-size: 14px;">内存</label>
                    <div id="detailMemory"></div>
                </div>
            </div>
            <div style="padding: 15px; background: #f5f5f5; border-radius: 6px;">
                <label style="color: #666; font-size: 14px; display: block; margin-bottom: 8px;">产品描述</label>
                <div id="detailDescription"></div>
            </div>
        </div>
        
        <!-- 批量操作按钮 -->
        <div style="margin-bottom: 20px; display: flex; gap: 10px;">
            <button class="btn btn-primary" onclick="batchOperation('online')">批量上架</button>
            <button class="btn btn-warning" onclick="batchOperation('offline')">批量下架</button>
            <button class="btn btn-success" onclick="batchOperation('price')">批量修改价格</button>
        </div>
        
        <!-- SKU列表 -->
        <div class="table-container">
            <table class="data-table">
                <thead>
                    <tr>
                        <th style="width: 120px;">SKU编号</th>
                        <th style="width: 120px;">数据中心</th>
                        
                        <th style="width: 120px;">包年包月价格</th>
                        <th style="width: 120px;">按量价格</th>
                        <th style="width: 80px;">总库存</th>
                        <th style="width: 80px;">剩余库存</th>
                        <th style="width: 80px;">上架状态</th>
                        <th style="width: 180px;">操作</th>
                    </tr>
                </thead>
                <tbody id="skuTableBody">
                    <!-- SKU数据将通过JavaScript动态填充 -->
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- 价格修改模态框 -->
<div class="modal" id="editPriceModal">
    <div class="modal-content">
        <div class="modal-header">
            <h3>修改SKU价格</h3>
            <button class="close-btn" onclick="closeModal('editPriceModal')">&times;</button>
        </div>
        <div class="modal-body">
            <input type="hidden" id="editPriceSkuId">
            <div class="form-group">
                <label>包年包月价格 (元/月)</label>
                <input type="number" id="monthlyPrice" class="form-control" min="0" step="0.01">
            </div>
            <div class="form-group">
                <label>按量价格 (元/小时)</label>
                <input type="number" id="hourlyPrice" class="form-control" min="0" step="0.01">
            </div>
        </div>
        <div class="modal-footer">
            <button class="btn btn-secondary" onclick="closeModal('editPriceModal')">取消</button>
            <button class="btn btn-primary" onclick="saveSkuPrice()">保存</button>
        </div>
    </div>
</div>

<!-- 库存修改模态框 -->
<div class="modal" id="editStockModal">
    <div class="modal-content">
        <div class="modal-header">
            <h3>修改SKU库存</h3>
            <button class="close-btn" onclick="closeModal('editStockModal')">&times;</button>
        </div>
        <div class="modal-body">
            <input type="hidden" id="editStockSkuId">
            <div class="form-group">
                <label>总库存</label>
                <input type="number" id="totalStock" class="form-control" min="0">
            </div>

        </div>
        <div class="modal-footer">
            <button class="btn btn-secondary" onclick="closeModal('editStockModal')">取消</button>
            <button class="btn btn-primary" onclick="saveSkuStock()">保存</button>
        </div>
    </div>
</div>

<!-- 批量操作确认模态框 -->
<div class="modal" id="batchConfirmModal">
    <div class="modal-content">
        <div class="modal-header">
            <h3 id="batchModalTitle">批量操作确认</h3>
            <button class="close-btn" onclick="closeModal('batchConfirmModal')">&times;</button>
        </div>
        <div class="modal-body">
            <input type="hidden" id="batchOperationType">
            <p id="batchConfirmMessage">确定要执行此批量操作吗？</p>
            <div id="batchPriceContainer" style="display: none;">
                <div class="form-group">
                    <label>新包年包月价格 (元/月)</label>
                    <input type="number" id="batchMonthlyPrice" class="form-control" min="0" step="0.01">
                </div>
                <div class="form-group">
                    <label>新按量价格 (元/小时)</label>
                    <input type="number" id="batchHourlyPrice" class="form-control" min="0" step="0.01">
                </div>
            </div>
        </div>
        <div class="modal-footer">
            <button class="btn btn-secondary" onclick="closeModal('batchConfirmModal')">取消</button>
            <button class="btn btn-primary" onclick="confirmBatchOperation()">确认</button>
        </div>
    </div>
</div>

<!-- 云硬盘产品详情页 -->
        <div class="page" id="disk-detail">
            <div class="card">
                <div class="card-title" style="display: flex; justify-content: space-between; align-items: center;">
                    <span id="diskDetailTitle">云硬盘产品详情</span>
                    <button class="btn" onclick="goBackToDiskList()">返回列表</button>
                </div>

                <!-- 产品基本信息 -->
                <div style="margin-bottom: 30px;">
                    <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 20px; margin-bottom: 20px;">
                        <div>
                            <label style="color: #666; font-size: 14px;">产品名称</label>
                            <div id="diskDetailProductName" style="font-weight: 600;"></div>
                        </div>
                        <div>
                            <label style="color: #666; font-size: 14px;">容量范围</label>
                            <div id="diskDetailCapacityRange" style="font-weight: 600;"></div>
                        </div>
                        <div>
                            <label style="color: #666; font-size: 14px;">步长</label>
                            <div id="diskDetailStep"></div>
                        </div>
                        <div>
                            <label style="color: #666; font-size: 14px;">类别</label>
                            <div id="diskDetailCategory"></div>
                        </div>
                        <div>
                            <label style="color: #666; font-size: 14px;">云硬盘类型</label>
                            <div id="diskDetailType"></div>
                        </div>
                        <div>
                            <label style="color: #666; font-size: 14px;">地域</label>
                            <div id="diskDetailRegion"></div>
                        </div>
                    </div>
                    <div style="padding: 15px; background: #f5f5f5; border-radius: 6px;">
                        <label style="color: #666; font-size: 14px; display: block; margin-bottom: 8px;">产品描述</label>
                        <div id="diskDetailDescription"></div>
                    </div>
                </div>

                <!-- 批量操作按钮 -->
                <div style="margin-bottom: 20px; display: flex; gap: 10px;">
                    <button class="btn btn-primary" onclick="batchDiskOperation('online')">批量上架</button>
                    <button class="btn btn-warning" onclick="batchDiskOperation('offline')">批量下架</button>
                    <button class="btn btn-success" onclick="batchDiskOperation('price')">批量修改价格</button>
                </div>

                <!-- SKU列表 -->
                <div class="table-container">
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th style="width: 120px;">SKU编号</th>
                                <th style="width: 120px;">数据中心</th>
                                
                                <th style="width: 140px;">包年包月价格</th>
                                <th style="width: 140px;">按量价格</th>
                                <th style="width: 100px;">总库存</th>
                                <th style="width: 100px;">剩余库存</th>
                                <th style="width: 80px;">上架状态</th>
                                <th style="width: 180px;">操作</th>
                            </tr>
                        </thead>
                        <tbody id="diskSkuTableBody">
                            <!-- SKU数据将通过JavaScript动态填充 -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- 其他页面占位 -->
        <div class="page" id="products">
            <div class="card">
                <div class="card-title">产品管理</div>
                <p style="color: #666; text-align: center; padding: 40px;">请选择左侧菜单中的具体产品类型...</p>
            </div>
        </div>

        <!-- 计算产品页面 -->
        <div class="page" id="cloud-servers">
            <div class="card">
                <div class="card-title" style="display: flex; justify-content: space-between; align-items: center;">
                    <span>计算产品管理</span>
                    <button class="btn btn-primary" onclick="showAddServerModal()">
                        <span style="margin-right: 4px;">➕</span>
                        新增计算产品
                    </button>
                </div>

                <!-- 计算产品列表 -->
                <div class="table-container">
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th style="width: 150px;">产品名称</th>
                                <th style="width: 120px;">套餐规格</th>

                                <th style="width: 100px;">CPU配置</th>
                                <th style="width: 80px;">内存</th>
                                <th style="width: 140px;">GPU配置</th>
                                <th style="width: 140px;">地域</th>
                                <th style="width: 200px;">产品描述</th>
                                <th style="width: 80px;">状态</th>
                                <th style="width: 180px;">操作</th>
                            </tr>
                        </thead>
                        <tbody id="serverProductsTable">
                            <tr>
                                <td>
                                    <div style="font-weight: 500; color: #1890ff; cursor: pointer;" onclick="showProductDetail(1)">通用均衡型G7</div>
                                </td>
                                <td>
                                    <div style="font-weight: 500; color: #52c41a;">G7.2B</div>
                                </td>

                                <td>
                                    <div style="font-weight: 500;">2核</div>
                                    <div style="color: #666; font-size: 12px;">Intel Xeon</div>
                                </td>
                                <td>
                                    <div style="font-weight: 500;">4GB</div>
                                </td>
                                <td>
                                    <span style="color: #999;">无GPU</span>
                                </td>
                                <td>
                                    <div style="font-weight: 500;">华东2</div>
                                </td>
                                <td>
                                    <div style="font-size: 13px; line-height: 1.4;">适用于中小型Web应用、轻量级数据库、开发测试环境</div>
                                </td>
                                <td>
                                    <span class="status-badge status-active">上架</span>
                                </td>
                                <td>
                                    <div class="action-buttons">
                                        <button class="btn-link" onclick="editServerRegion(1)">新增地域</button>
                                        <button class="btn-link" onclick="toggleServerStatus(1)">下架</button>
                                        <button class="btn-link danger" onclick="deleteServerProduct(1)">删除</button>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    <div style="font-weight: 500; color: #1890ff; cursor: pointer;" onclick="showProductDetail(2)">GPU加速型GN7i</div>
                                </td>
                                <td>
                                    <div style="font-weight: 500; color: #52c41a;">GN7i.4L</div>
                                </td>

                                <td>
                                    <div style="font-weight: 500;">4核</div>
                                    <div style="color: #666; font-size: 12px;">Intel Xeon</div>
                                </td>
                                <td>
                                    <div style="font-weight: 500;">16GB</div>
                                </td>
                                <td>
                                    <div style="color: #52c41a; font-weight: 500;">NVIDIA T4</div>
                                    <div style="color: #666; font-size: 12px;">16GB显存</div>
                                </td>
                                <td>
                                    <div style="font-weight: 500;">华东2</div>
                                </td>
                                <td>
                                    <div style="font-size: 13px; line-height: 1.4;">专为AI训练、深度学习推理、科学计算等GPU密集型应用设计</div>
                                </td>
                                <td>
                                    <span class="status-badge status-active">上架</span>
                                </td>
                                <td>
                                    <div class="action-buttons">
                                        <button class="btn-link" onclick="editServerRegion(2)">新增地域</button>
                                        <button class="btn-link" onclick="toggleServerStatus(2)">下架</button>
                                        <button class="btn-link danger" onclick="deleteServerProduct(2)">删除</button>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    <div style="font-weight: 500; color: #1890ff; cursor: pointer;" onclick="showProductDetail(3)">计算优化型C9a</div>
                                </td>
                                <td>
                                    <div style="font-weight: 500; color: #52c41a;">C9a.2B</div>
                                </td>
                                <td>
                                    <div style="font-weight: 500;">x86_64</div>
                                </td>
                                <td>
                                    <div style="font-weight: 500; color: #fa8c16;">计算优化型</div>
                                </td>
                                <td>
                                    <div style="font-weight: 500;">8核</div>
                                    <div style="color: #666; font-size: 12px;">Intel Xeon</div>
                                </td>
                                <td>
                                    <div style="font-weight: 500;">16GB</div>
                                </td>
                                <td>
                                    <span style="color: #999;">无GPU</span>
                                </td>
                                <td>
                                    <div style="font-weight: 500;">华北2</div>
                                </td>
                                <td>
                                    <div style="font-size: 13px; line-height: 1.4;">专为高性能计算、科学建模、批处理等CPU密集型应用优化</div>
                                </td>
                                <td>
                                    <span class="status-badge status-inactive">下架</span>
                                </td>
                                <td>
                                    <div class="action-buttons">
                                        <button class="btn-link" onclick="editServerRegion(3)">新增地域</button>
                                        <button class="btn-link" onclick="toggleServerStatus(3)">上架</button>
                                        <button class="btn-link danger" onclick="deleteServerProduct(3)">删除</button>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <!-- 分页 -->
                <div class="pagination">
                    <span class="pagination-info">共 3 条记录</span>
                    <div class="pagination-controls">
                        <button class="pagination-btn" disabled>上一页</button>
                        <span class="pagination-current">1</span>
                        <button class="pagination-btn" disabled>下一页</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 云硬盘产品页面 -->
        <div class="page" id="cloud-disks">
            <div class="card">
                <div class="card-title" style="display: flex; justify-content: space-between; align-items: center;">
                    <span>云硬盘产品管理</span>
                    <button class="btn btn-primary" onclick="showAddDiskModal()">
                        <span style="margin-right: 4px;">➕</span>
                        新增云硬盘产品
                    </button>
                </div>

                <!-- 云硬盘产品列表 -->
                <div class="table-container">
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th style="width: 150px;">产品名称</th>
                                <th style="width: 120px;">容量</th>
                                <th style="width: 80px;">步长</th>
                                <th style="width: 100px;">类别</th>
                                <th style="width: 120px;">云硬盘类型</th>
                                <th style="width: 140px;">地域</th>
                                <th style="width: 200px;">产品描述</th>
                                <th style="width: 80px;">状态</th>
                                <th style="width: 150px;">操作</th>
                            </tr>
                        </thead>
                        <tbody id="diskProductsTable">
                            <tr>
                                <td>
                                    <div style="font-weight: 500; color: #1890ff; cursor: pointer;" onclick="showDiskProductDetail(1)">高性能云硬盘SSD</div>
                                </td>
                                <td>100-500GB</td>
                                <td>100</td>
                                <td>数据盘</td>
                                <td>SSD</td>
                                <td>
                                    <div style="font-weight: 500;">华东2</div>
                                </td>
                                <td>
                                    <div style="font-size: 13px; line-height: 1.4;">适用于IO密集型应用，提供高性能和低延迟</div>
                                </td>
                                <td>
                                    <span class="status-badge status-active">上架</span>
                                </td>
                                <td>
                                    <div class="action-buttons">
                                        <button class="btn-link" onclick="editDiskProduct(1)">编辑</button>
                                        <button class="btn-link" onclick="toggleDiskStatus(1)">下架</button>
                                        <button class="btn-link danger" onclick="deleteDiskProduct(1)">删除</button>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    <div style="font-weight: 500; color: #1890ff; cursor: pointer;" onclick="showDiskProductDetail(2)">通用型云硬盘HDD</div>
                                </td>
                                <td>200-1000GB</td>
                                <td>50</td>
                                <td>系统盘</td>
                                <td>普通</td>
                                <td>
                                    <div style="font-weight: 500;">华北2</div>
                                </td>
                                <td>
                                    <div style="font-size: 13px; line-height: 1.4;">性价比高，适用于大容量存储需求</div>
                                </td>
                                <td>
                                    <span class="status-badge status-active">上架</span>
                                </td>
                                <td>
                                    <div class="action-buttons">
                                        <button class="btn-link" onclick="editDiskProduct(2)">编辑</button>
                                        <button class="btn-link" onclick="toggleDiskStatus(2)">下架</button>
                                        <button class="btn-link danger" onclick="deleteDiskProduct(2)">删除</button>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    <div style="font-weight: 500; color: #1890ff; cursor: pointer;" onclick="showDiskProductDetail(3)">极速型云硬盘NVMe</div>
                                </td>
                                <td>500-2000GB</td>
                                <td>200</td>
                                <td>数据盘</td>
                                <td>高效</td>
                                <td>
                                    <div style="font-weight: 500;">华南1</div>
                                </td>
                                <td>
                                    <div style="font-size: 13px; line-height: 1.4;">超高性能云硬盘，适用于关键业务和数据库</div>
                                </td>
                                <td>
                                    <span class="status-badge status-inactive">下架</span>
                                </td>
                                <td>
                                    <div class="action-buttons">
                                        <button class="btn-link" onclick="editDiskProduct(3)">编辑</button>
                                        <button class="btn-link" onclick="toggleDiskStatus(3)">上架</button>
                                        <button class="btn-link danger" onclick="deleteDiskProduct(3)">删除</button>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <!-- 分页 -->
                <div class="pagination">
                    <span class="pagination-info">共 3 条记录</span>
                    <div class="pagination-controls">
                        <button class="pagination-btn" disabled>上一页</button>
                        <span class="pagination-current">1</span>
                        <button class="pagination-btn" disabled>下一页</button>
                    </div>
                </div>
            </div>
        </div>

        <div class="page" id="customers">
            <div class="card">
                <div class="card-title">客户管理</div>
                <p style="color: #666; text-align: center; padding: 40px;">客户管理功能开发中...</p>
            </div>
        </div>

        <div class="page" id="reports">
            <div class="card">
                <div class="card-title">报表统计</div>
                <p style="color: #666; text-align: center; padding: 40px;">报表统计功能开发中...</p>
            </div>
        </div>
    </main>

    <!-- 云硬盘详情模态框 -->
    <div class="modal" id="diskDetailModal" style="display: none;">
        <div class="modal-content" style="width: 800px;">
            <div class="modal-header">
                <h3 class="modal-title" id="detailTitle">云硬盘产品详情</h3>
                <button class="modal-close" onclick="closeModal('diskDetailModal')">&times;</button>
            </div>
            <div class="modal-body">
                <div class="detail-grid">
                    <div class="detail-card">
                        <h4 class="card-title">基本信息</h4>
                        <div class="spec-item">
                            <div class="spec-label">产品ID:</div>
                            <div class="spec-value" id="detailProductId">--</div>
                        </div>
                        <div class="spec-item">
                            <div class="spec-label">产品名称:</div>
                            <div class="spec-value" id="detailProductName">--</div>
                        </div>
                        <div class="spec-item">
                            <div class="spec-label">类别:</div>
                            <div class="spec-value" id="detailCategory">--</div>
                        </div>
                        <div class="spec-item">
                            <div class="spec-label">云硬盘类型:</div>
                            <div class="spec-value" id="detailDiskType">--</div>
                        </div>
                        <div class="spec-item">
                            <div class="spec-label">容量范围:</div>
                            <div class="spec-value" id="detailCapacityRange">--</div>
                        </div>
                        <div class="spec-item">
                            <div class="spec-label">步长:</div>
                            <div class="spec-value" id="detailDiskStep">--</div>
                        </div>
                    </div>
                    <div class="detail-card">
                        <h4 class="card-title">地域</h4>
                        <div class="spec-item">
                            <div class="spec-label">地域:</div>
                            <div class="spec-value" id="detailRegion">--</div>
                        </div>

                    </div>
                    <div class="detail-card" style="grid-column: span 2;">
                        <h4 class="card-title">产品描述</h4>
                        <div class="spec-value" id="detailDescription" style="white-space: pre-line;">--</div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="closeModal('diskDetailModal')">返回列表</button>
                <button class="btn btn-secondary" onclick="editDiskProduct(document.getElementById('detailProductId').textContent)">编辑</button>
                <button class="btn btn-primary" id="detailStatusToggleBtn">下架</button>
            </div>
        </div>
    </div>

    <!-- 新增云硬盘模态框 -->
    <div class="modal" id="addDiskModal">
        <div class="modal-content" style="width: 600px; max-width: 90vw;">
            <div class="modal-header">
                <h3 class="modal-title">新增云硬盘产品</h3>
                <button class="modal-close" onclick="closeModal('addDiskModal')">&times;</button>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <label class="form-label">产品名称</label>
                    <input type="text" class="form-input" id="diskProductName" required>
                </div>
                <div class="form-group">
                    <label class="form-label">容量范围 (GB)</label>
                    <div style="display: flex; gap: 10px;">
                        <input type="number" class="form-input" id="minCapacity" placeholder="最小容量" min="10" required>
                        <span style="align-self: center;">-</span>
                        <input type="number" class="form-input" id="maxCapacity" placeholder="最大容量" min="10" required>
                    </div>
                </div>
                <div class="form-group">
                    <label class="form-label">步长 (GB) <span class="text-danger">*</span></label>
                    <input type="number" class="form-input" id="diskStep" min="1" step="1" value="50" required>
                    <div style="font-size: 12px; color: #666; margin-top: 5px;">每次扩容的步长值，建议设置为最小容量的10%-20%</div>
                </div>
                <div class="form-group">
                    <label class="form-label">云硬盘类型</label>
                    <select class="form-input" id="diskType" required>
                        <option value="普通">普通</option>
                        <option value="高效">高效</option>
                        <option value="SSD">SSD</option>
                    </select>
                </div>
                <div class="form-group">
                    <label class="form-label">类别 <span class="text-danger">*</span></label>
                    <div class="radio-group" style="display: flex; gap: 20px; margin-top: 8px;">
                        <label style="display: flex; align-items: center; cursor: pointer;">
                            <input type="radio" name="category" value="system" checked style="margin-right: 8px; width: 16px; height: 16px;">
                            <span>系统盘</span>
                        </label>
                        <label style="display: flex; align-items: center; cursor: pointer;">
                            <input type="radio" name="category" value="data" style="margin-right: 8px; width: 16px; height: 16px;">
                            <span>数据盘</span>
                        </label>
                    </div>
                </div>

                <div class="form-group">
                    <label class="form-label">地域（可多选）</label>
                    <div class="multi-select-container">
                        <div class="checkbox-grid">
                            <label class="checkbox-item">
                                <input type="checkbox" name="diskRegion" value="华东2">华东2（上海）
                            </label>
                            <label class="checkbox-item">
                                <input type="checkbox" name="diskRegion" value="华北2">华北2（北京）
                            </label>
                            <label class="checkbox-item">
                                <input type="checkbox" name="diskRegion" value="华南1">华南1（深圳）
                            </label>
                        </div>
                    </div>
                </div>
                <div class="form-group">
                    <label class="form-label">数据中心（可多选）</label>
                    <div class="multi-select-container">
                        <div class="checkbox-grid">

                        </div>
                    </div>
                </div>

                <div class="form-group">
                    <label class="form-label">价格设置 <span class="text-danger">*</span></label>
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px;">
                        <div>
                            <label style="font-size: 13px; color: #666; margin-bottom: 5px; display: block;">包年包月价格 (元/GB/月)</label>
                            <input type="number" class="form-input" id="diskMonthlyPrice" step="0.01" min="0" placeholder="0.45" required>
                        </div>
                        <div>
                            <label style="font-size: 13px; color: #666; margin-bottom: 5px; display: block;">按量价格 (元/GB/时)</label>
                            <input type="number" class="form-input" id="diskHourlyPrice" step="0.0001" min="0" placeholder="0.0006" required>
                        </div>
                    </div>
                    <div style="font-size: 12px; color: #666; margin-top: 5px;">
                        💡 价格按每GB容量计费，用户购买时会根据选择的容量自动计算总价
                    </div>
                </div>

                <div class="form-group">
                    <label class="form-label">产品描述</label>
                    <textarea class="form-input" id="diskDescription" rows="3" placeholder="详细描述产品的适用场景、性能特点和技术优势"></textarea>
                </div>
                <div class="form-group">
                    <label class="form-label">上架状态</label>
                    <select class="form-input" id="diskStatus" required>
                        <option value="active">上架</option>
                        <option value="inactive">下架</option>
                    </select>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn" onclick="closeModal('addDiskModal')">取消</button>
                <button class="btn btn-primary" onclick="saveNewDiskProduct()">保存</button>
            </div>
        </div>
    </div>







    <script>
        // 切换页面显示函数
        function switchPage(pageId, clickedItem) {
            console.log('切换到页面:', pageId);

            // 隐藏所有页面
            document.querySelectorAll('.page').forEach(page => {
                page.classList.remove('active');
            });

            // 显示目标页面
            const targetPage = document.getElementById(pageId);
            if (targetPage) {
                targetPage.classList.add('active');
                console.log('页面切换成功:', pageId);
            } else {
                console.error('未找到页面:', pageId);
            }

            // 更新导航菜单项激活状态
            document.querySelectorAll('.menu-item, .submenu-item').forEach(item => {
                item.classList.remove('active');
            });

            // 激活当前点击的菜单项
            if (clickedItem) {
                clickedItem.classList.add('active');
                console.log('激活菜单项:', clickedItem.textContent.trim());
            }
        }

        // 初始化导航功能
        function initNavigation() {
            const menuItems = document.querySelectorAll('.menu-item[data-page]');
            const submenuItems = document.querySelectorAll('.submenu-item[data-page]');

            // 主菜单项点击事件
            menuItems.forEach(item => {
                item.addEventListener('click', function(event) {
                    event.stopPropagation();
                    const targetPage = this.getAttribute('data-page');
                    switchPage(targetPage, this);
                });
            });

            // 二级菜单项点击事件
            submenuItems.forEach(item => {
                item.addEventListener('click', function(event) {
                    event.stopPropagation();
                    const targetPage = this.getAttribute('data-page');
                    switchPage(targetPage, this);
                });
            });
        }

        // 二级菜单切换功能
        function toggleSubmenu(menuItem) {
            const submenu = menuItem.nextElementSibling;
            const isExpanded = menuItem.classList.contains('expanded');

            if (isExpanded) {
                menuItem.classList.remove('expanded');
                submenu.classList.remove('expanded');
            } else {
                menuItem.classList.add('expanded');
                submenu.classList.add('expanded');
            }
        }

        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM已加载完成，开始初始化菜单');

            // 初始化导航功能
            initNavigation();

            // 产品管理子菜单展开/折叠
            const productMenu = document.querySelector('.menu-item.has-submenu');
            const submenu = document.getElementById('products-submenu');

            if (productMenu && submenu) {
                productMenu.addEventListener('click', function(event) {
                    event.stopPropagation();
                    toggleSubmenu(this);
                    console.log('产品管理菜单展开状态:', this.classList.contains('expanded'));
                });
            }



            // 初始化订单列表
            if (typeof renderOrders === 'function') {
                renderOrders();
                console.log('订单列表已初始化');
            } else {
                console.error('未找到renderOrders函数');
            }
        });

        // 模拟订单数据
        let orders = [
            {
                id: 'ORD-2024-001',
                customerName: '张三',
                customerEmail: '<EMAIL>',
                orderType: '新购',
                skus: [
                    {
                        skuCode: 'ECS-C2M4-HZ2A-001',
                        productType: '云服务器ECS',
                        productSpec: '2核4GB 通用型',
                        billingMethod: '包年包月',
                        duration: '1年',
                        quantity: 1,
                        amount: 2880.00,
                        resourceStatus: '运行中'
                    },
                    {
                        skuCode: 'EBS-SSD-HZ2A-004',
                        productType: '云硬盘SSD',
                        productSpec: '100GB SSD',
                        billingMethod: '包年包月',
                        duration: '1年',
                        quantity: 1,
                        amount: 540.00,
                        resourceStatus: '运行中'
                    }
                ],
                duration: '1年',
                amount: 3420.00,
                originalAmount: 3420.00,
                status: 'paid',
                createTime: '2024-01-15 10:30:00',
                payTime: '2024-01-15 10:45:00',
                notes: '云服务器+云硬盘套餐'
            },
            {
                id: 'ORD-2024-002',
                customerName: '李四',
                customerEmail: '<EMAIL>',
                orderType: '续费',
                skus: [
                    {
                        skuCode: 'RDS-MYSQL57-HB2A-001',
                        productType: '云数据库RDS',
                        productSpec: 'MySQL 5.7 2核4GB',
                        billingMethod: '包年包月',
                        duration: '6个月',
                        quantity: 1,
                        amount: 1680.00,
                        resourceStatus: '运行中'
                    }
                ],
                duration: '6个月',
                amount: 1680.00,
                originalAmount: 1800.00,
                status: 'paid',
                createTime: '2024-01-14 15:20:00',
                payTime: '2024-01-14 15:45:00',
                notes: '已享受新客户优惠'
            },
            {
                id: 'ORD-2024-003',
                customerName: '王五',
                customerEmail: '<EMAIL>',
                orderType: '新购',
                skus: [
                    {
                        skuCode: 'ECS-C4M8-HZ2B-001',
                        productType: '云服务器ECS',
                        productSpec: '4核8GB 计算型',
                        billingMethod: '包年包月',
                        duration: '6个月',
                        quantity: 1,
                        amount: 2592.00,
                        resourceStatus: '待创建'
                    },
                    {
                        skuCode: 'EBS-SSD-HZ2B-005',
                        productType: '云硬盘SSD',
                        productSpec: '200GB SSD',
                        billingMethod: '包年包月',
                        duration: '6个月',
                        quantity: 1,
                        amount: 1080.00,
                        resourceStatus: '待创建'
                    }
                ],
                duration: '6个月',
                amount: 3672.00,
                originalAmount: 3672.00,
                status: 'pending',
                createTime: '2024-01-18 16:10:00',
                payTime: '',
                notes: '云服务器+云硬盘套餐'
            }
        ];

        let currentPage = 1;
        let pageSize = 10;
        let filteredOrders = [...orders];



        // 渲染订单列表
        function renderOrders() {
            const tbody = document.getElementById('ordersTableBody');
            if (!tbody) {
                return;
            }
            tbody.innerHTML = '';

            const startIndex = (currentPage - 1) * pageSize;
            const endIndex = startIndex + pageSize;
            const pageOrders = filteredOrders.slice(startIndex, endIndex);

            pageOrders.forEach(order => {
                // 为每个SKU创建一行
                order.skus.forEach((sku, index) => {
                    const row = document.createElement('tr');
                    row.innerHTML = `
                        <td>
                            ${index === 0 ? `<span style="color: #1890ff; cursor: pointer;" onclick="showOrderDetail('${order.id}')">${order.id}</span>` : ''}
                        </td>
                        <td>${index === 0 ? order.customerName : ''}</td>
                        <td>
                            ${index === 0 ? `<span class="order-type-tag order-type-${order.orderType}">${order.orderType}</span>` : ''}
                        </td>
                        <td>
                            <div>${sku.productType}</div>
                            <div style="color: #666; font-size: 12px;">${sku.productSpec}</div>
                        </td>
                        <td>
                            <span style="font-family: monospace; font-size: 12px; color: #666;">${sku.skuCode}</span>
                        </td>
                        <td>
                            ${index === 0 ? `
                                <div style="font-weight: 600;">¥${order.amount.toFixed(2)}</div>
                                ${order.amount !== order.originalAmount ? `<div style="color: #666; font-size: 12px; text-decoration: line-through;">¥${order.originalAmount.toFixed(2)}</div>` : ''}
                            ` : ''}
                        </td>
                        <td>
                            ${index === 0 ? `<span class="status-tag ${getStatusClass(order.status)}">${getStatusText(order.status)}</span>` : ''}
                        </td>
                        <td>${index === 0 ? order.createTime : ''}</td>
                        <td>
                            ${index === 0 ? `<button class="btn" onclick="showOrderDetail('${order.id}')">查看</button>` : ''}
                        </td>
                    `;
                    tbody.appendChild(row);
                });
            });
        }

        // 获取状态样式类
        function getStatusClass(status) {
            const statusMap = {
                'pending': 'status-pending',
                'paid': 'status-paid',
                'completed': 'status-completed',
                'cancelled': 'status-cancelled'
            };
            return statusMap[status] || '';
        }

        // 获取状态文本
        function getStatusText(status) {
            const statusMap = {
                'pending': '待付款',
                'paid': '已付款',
                'completed': '已完成',
                'cancelled': '已取消'
            };
            return statusMap[status] || status;
        }

        // 搜索订单
        function searchOrders() {
            const searchText = document.getElementById('searchInput').value.toLowerCase();
            const statusFilter = document.getElementById('statusFilter').value;
            const productFilter = document.getElementById('productFilter').value;

            filteredOrders = orders.filter(order => {
                const matchSearch = !searchText ||
                    order.id.toLowerCase().includes(searchText) ||
                    order.customerName.toLowerCase().includes(searchText);

                const matchStatus = !statusFilter || order.status === statusFilter;

                const matchProduct = !productFilter ||
                    order.productType.toLowerCase().includes(productFilter);

                return matchSearch && matchStatus && matchProduct;
            });

            currentPage = 1;
            renderOrders();
        }

        // 重置筛选
        function resetFilters() {
            document.getElementById('searchInput').value = '';
            document.getElementById('statusFilter').value = '';
            document.getElementById('productFilter').value = '';
            filteredOrders = [...orders];
            currentPage = 1;
            renderOrders();
        }

        // 分页功能
        function changePage(direction) {
            const totalPages = Math.ceil(filteredOrders.length / pageSize);
            const newPage = currentPage + direction;

            if (newPage >= 1 && newPage <= totalPages) {
                currentPage = newPage;
                renderOrders();
            }
        }

        // 显示订单详情
        function showOrderDetail(orderId) {
            const order = orders.find(o => o.id === orderId);
            if (!order) return;

            // 隐藏所有页面
            document.querySelectorAll('.page').forEach(page => page.classList.remove('active'));

            // 显示订单详情页
            document.getElementById('order-detail').classList.add('active');

            // 填充订单基本信息
            document.getElementById('detailOrderId').textContent = order.id;
            document.getElementById('detailCustomerName').textContent = order.customerName;
            document.getElementById('detailOrderType').innerHTML = `<span class="order-type-tag order-type-${order.orderType}">${order.orderType}</span>`;
            document.getElementById('detailOrderStatus').innerHTML = `<span class="status-tag ${getStatusClass(order.status)}">${getStatusText(order.status)}</span>`;
            document.getElementById('detailOrderAmount').textContent = `¥${order.amount.toFixed(2)}`;
            document.getElementById('detailCreateTime').textContent = order.createTime;
            document.getElementById('detailPayTime').textContent = order.payTime || '未付款';
            document.getElementById('detailCustomerEmail').textContent = order.customerEmail;
            document.getElementById('detailNotes').textContent = order.notes || '无';

            // 填充子订单列表
            renderOrderDetailSkuTable(order);
        }

        // 渲染订单详情SKU表格
        function renderOrderDetailSkuTable(order) {
            const tbody = document.getElementById('orderDetailSkuTable');
            tbody.innerHTML = '';

            order.skus.forEach(sku => {
                // 获取产品信息和SKU信息
                const productInfo = getProductInfoBySkuCode(sku.skuCode);
                const skuInfo = getSkuInfoBySkuCode(sku.skuCode);

                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>
                        <div style="font-weight: 500; margin-bottom: 4px;">${productInfo ? productInfo.productName : sku.productType}</div>
                        <div style="color: #666; font-size: 12px; line-height: 1.4;">
                            ${productInfo ? renderProductDetails(productInfo) : sku.productSpec}
                        </div>
                    </td>
                    <td>
                        <div style="font-family: monospace; font-size: 12px; color: #1890ff; margin-bottom: 4px;">${sku.skuCode}</div>
                        <div style="color: #666; font-size: 11px; line-height: 1.3;">
                            ${renderSkuDetails(skuInfo)}
                        </div>
                    </td>
                    <td>${sku.billingMethod || '包年包月'}</td>
                    <td>${sku.duration || order.duration}</td>
                    <td>${sku.quantity || 1}</td>
                    <td style="font-weight: 600;">¥${sku.amount.toFixed(2)}</td>
                    <td>
                        <span class="status-tag ${getResourceStatusClass(sku.resourceStatus || '待创建')}">${sku.resourceStatus || '待创建'}</span>
                    </td>
                `;
                tbody.appendChild(row);
            });
        }

        // 渲染产品详细信息
        function renderProductDetails(productInfo) {
            const details = [];

            if (productInfo.spec) details.push(`规格: ${productInfo.spec}`);
            if (productInfo.cpu) details.push(`CPU: ${productInfo.cpu}`);
            if (productInfo.memory) details.push(`内存: ${productInfo.memory}`);
            if (productInfo.diskType) details.push(`类型: ${productInfo.diskType}`);
            if (productInfo.capacityRange) details.push(`容量: ${productInfo.capacityRange}`);
            if (productInfo.type) details.push(`类型: ${productInfo.type}`);
            if (productInfo.bandwidth) details.push(`带宽: ${productInfo.bandwidth}`);
            if (productInfo.capacity) details.push(`容量: ${productInfo.capacity}`);
            if (productInfo.protection) details.push(`防护: ${productInfo.protection}`);

            return details.join('<br>');
        }

        // 渲染SKU详细信息
        function renderSkuDetails(skuInfo) {
            const details = [];

            if (skuInfo.region) details.push(`区域: ${skuInfo.region}`);
            
            if (skuInfo.monthlyPrice > 0) details.push(`月价: ¥${skuInfo.monthlyPrice}`);
            if (skuInfo.hourlyPrice > 0) details.push(`时价: ¥${skuInfo.hourlyPrice}`);
            if (skuInfo.stock >= 0) details.push(`库存: ${skuInfo.stock}`);
            if (skuInfo.status && skuInfo.status !== 'unknown') {
                const statusText = skuInfo.status === 'active' ? '已上架' : '已下架';
                details.push(`状态: ${statusText}`);
            }

            return details.join('<br>');
        }

        // 获取资源状态样式类
        function getResourceStatusClass(status) {
            const statusMap = {
                '待创建': 'status-pending',
                '创建中': 'status-creating',
                '运行中': 'status-running',
                '已停止': 'status-stopped',
                '已释放': 'status-cancelled'
            };
            return statusMap[status] || 'status-pending';
        }

        // 返回订单列表
        function goBackToOrderList() {
            document.querySelectorAll('.page').forEach(page => page.classList.remove('active'));
            document.getElementById('orders').classList.add('active');
        }

        // 根据SKU编号获取产品信息
        function getProductInfoBySkuCode(skuCode) {
            // 计算产品映射
            const ecsProducts = {
                'ECS-C2M4-HZ2A-001': { productId: 1, productName: '通用均衡型G7', spec: 'G7.2B', cpu: '2核 Intel Xeon', memory: '4GB' },
                'ECS-C2M4-HZ2B-002': { productId: 1, productName: '通用均衡型G7', spec: 'G7.2B', cpu: '2核 Intel Xeon', memory: '4GB' },
                'ECS-C2M4-HB2A-003': { productId: 1, productName: '通用均衡型G7', spec: 'G7.2B', cpu: '2核 Intel Xeon', memory: '4GB' },
                'ECS-C4M8-HZ2B-001': { productId: 2, productName: 'GPU加速型GN7i', spec: 'GN7i.4L', cpu: '4核 Intel Xeon', memory: '16GB' },
                'ECS-C4M8-HB2C-002': { productId: 2, productName: 'GPU加速型GN7i', spec: 'GN7i.4L', cpu: '4核 Intel Xeon', memory: '16GB' },
                'ECS-C8M16-HB2C-001': { productId: 3, productName: '计算优化型C9a', spec: 'C9a.2B', cpu: '8核 Intel Xeon', memory: '16GB' },
                'ECS-UPG-C2M4-C4M8-001': { productId: 1, productName: '通用均衡型G7', spec: 'G7.2B → GN7i.4L', cpu: '2核→4核 Intel Xeon', memory: '4GB→16GB' }
            };

            // 云硬盘产品映射
            const diskProducts = {
                'EBS-SSD-HZ2A-004': { productId: 1, productName: '高性能云硬盘SSD', diskType: 'SSD', capacityRange: '100-500GB' },
                'EBS-SSD-HZ2B-005': { productId: 1, productName: '高性能云硬盘SSD', diskType: 'SSD', capacityRange: '100-500GB' },
                'EBS-UPG-100G-500G-001': { productId: 1, productName: '高性能云硬盘SSD', diskType: 'SSD', capacityRange: '100GB→500GB' }
            };

            // 其他产品映射
            const otherProducts = {
                'RDS-MYSQL57-HB2A-001': { productName: '云数据库RDS MySQL', spec: 'MySQL 5.7', cpu: '2核', memory: '4GB' },
                'RDS-MYSQL80-HB2B-001': { productName: '云数据库RDS MySQL', spec: 'MySQL 8.0', cpu: '4核', memory: '8GB' },
                'OSS-STD-HZ2A-001': { productName: '对象存储OSS', spec: '标准存储', capacity: '500GB' },
                'OSS-STD-HZ2B-002': { productName: '对象存储OSS', spec: '标准存储', capacity: '1TB' },
                'CDN-DOM-HZ2A-001': { productName: 'CDN加速服务', spec: '国内加速', bandwidth: '1TB流量包' },
                'CDN-GLB-HZ2A-001': { productName: 'CDN加速服务', spec: '全球加速', bandwidth: '5TB流量包' },
                'SLB-STD-HZ2B-001': { productName: '负载均衡SLB', spec: '应用型负载均衡ALB', type: '标准型' },
                'WAF-STD-HZ2A-001': { productName: '安全防护WAF', spec: '标准版WAF', protection: '基础防护' }
            };

            // 查找产品信息
            return ecsProducts[skuCode] || diskProducts[skuCode] || otherProducts[skuCode] || null;
        }

        // 根据SKU编号获取SKU详细信息
        function getSkuInfoBySkuCode(skuCode) {
            // 从全局SKU数据中查找
            for (const productId in skuData) {
                const skus = skuData[productId];
                const sku = skus.find(s => s.skuCode === skuCode);
                if (sku) {
                    return {
                        skuCode: sku.skuCode,
                        region: sku.region,
                        monthlyPrice: sku.monthlyPrice,
                        hourlyPrice: sku.hourlyPrice,
                        stock: sku.remainingStock,
                        status: sku.status
                    };
                }
            }

            // 如果在全局SKU数据中找不到，返回基本信息
            const regionMap = {
                'HZ2': '华东2',
                'HB2': '华北2'
            };

            const regionCode = skuCode.match(/-([A-Z0-9]{2})[A-Z]?-\d+$/)?.[1];
            const region = regionMap[regionCode] || '未知区域';

            return {
                skuCode: skuCode,
                region: region,
                monthlyPrice: 0,
                hourlyPrice: 0,
                stock: 0,
                status: 'unknown'
            };
        }







        // 显示模态框
        function showModal(modalId) {
            document.getElementById(modalId).style.display = 'flex';
        }

        // 关闭模态框
        function closeModal(modalId) {
            document.getElementById(modalId).style.display = 'none';
        }

        // 点击模态框背景关闭
        document.addEventListener('click', function(e) {
            if (e.target.classList.contains('modal')) {
                e.target.style.display = 'none';
            }
        });

        // 搜索框回车事件
        document.getElementById('searchInput').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                searchOrders();
            }
        });


        // 产品详情页功能
function showProductDetail(productId) {
    // 隐藏所有页面
    document.querySelectorAll('.page').forEach(page => page.classList.remove('active'));
    
    // 显示产品详情页
    document.getElementById('product-detail').classList.add('active');
    
    // 模拟加载产品数据
    const product = getProductById(productId);
    if (product) {
        document.getElementById('productDetailTitle').textContent = product.name + ' - 详情';
        document.getElementById('detailProductName').textContent = product.name;
        document.getElementById('detailPackageSpec').textContent = product.spec;
        document.getElementById('detailArchitecture').textContent = product.architecture;
        document.getElementById('detailSpecType').textContent = product.type;
        document.getElementById('detailCpuConfig').textContent = product.cpu;
        document.getElementById('detailMemory').textContent = product.memory;
        document.getElementById('detailDescription').textContent = product.description;
        
        // 加载SKU列表
        renderSkuList(productId);
    }
}

function goBackToProductList() {
    // 隐藏详情页，显示计算产品列表
    document.querySelectorAll('.page').forEach(page => page.classList.remove('active'));
    document.getElementById('cloud-servers').classList.add('active');
}

function getProductById(id) {
    // 模拟产品数据
    const products = [
        {
            id: 1,
            name: '通用均衡型G7',
            spec: 'G7.2B',
            architecture: 'x86_64',
            type: '通用均衡型',
            cpu: '2核 Intel Xeon',
            memory: '4GB',
            description: '适用于中小型Web应用、轻量级数据库、开发测试环境'
        },
        {
            id: 2,
            name: 'GPU加速型GN7i',
            spec: 'GN7i.4L',
            architecture: 'x86_64',
            type: 'GPU加速型',
            cpu: '4核 Intel Xeon',
            memory: '16GB',
            description: '专为AI训练、深度学习推理、科学计算等GPU密集型应用设计'
        },
        {
            id: 3,
            name: '计算优化型C9a',
            spec: 'C9a.2B',
            architecture: 'x86_64',
            type: '计算优化型',
            cpu: '8核 Intel Xeon',
            memory: '16GB',
            description: '专为高性能计算、科学建模、批处理等CPU密集型应用优化'
        }
    ];
    return products.find(p => p.id === id);
}

function renderSkuList(productId) {
    const tbody = document.getElementById('skuTableBody');
    tbody.innerHTML = '';
    
    // 模拟SKU数据
    const skus = getSkuByProductId(productId);
    
    skus.forEach(sku => {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td><span style="font-family: monospace; font-size: 12px; color: #666;">${sku.skuCode}</span></td>
            <td>${sku.region}</td>
            <td>¥${sku.monthlyPrice.toFixed(2)}</td>
            <td>¥${sku.hourlyPrice.toFixed(2)}/时</td>
            <td>${sku.totalStock}</td>
            <td>${sku.remainingStock}</td>
            <td><span class="status-badge status-${sku.status}">${sku.status === 'active' ? '已上架' : '已下架'}</span></td>
            <td>
                <div class="action-buttons">
                    <button class="btn-link" onclick="editSkuPrice(${sku.id})">修改价格</button>
                    <button class="btn-link" onclick="editSkuStock(${sku.id})">修改库存</button>
                    <button class="btn-link ${sku.status === 'active' ? 'danger' : ''}" onclick="toggleSkuStatus(${sku.id}, '${sku.status}')">
                        ${sku.status === 'active' ? '下架' : '上架'}
                    </button>
                </div>
            </td>
        `;
        tbody.appendChild(row);
    });
}

// 全局SKU数据
const skuData = {
    1: [
        { id: 101, skuCode: 'ECS-C2M4-HZ2-001', productId: 1, region: '华东2', monthlyPrice: 324, hourlyPrice: 0.45, totalStock: 100, remainingStock: 78, status: 'active' },
        { id: 102, skuCode: 'ECS-C2M4-HZ2-002', productId: 1, region: '华东2', monthlyPrice: 324, hourlyPrice: 0.45, totalStock: 100, remainingStock: 65, status: 'active' },
        { id: 103, skuCode: 'ECS-C2M4-HB2-003', productId: 1, region: '华北2', monthlyPrice: 339, hourlyPrice: 0.47, totalStock: 80, remainingStock: 42, status: 'active' }
    ],
    2: [
        { id: 201, skuCode: 'ECS-C4M8-HZ2-001', productId: 2, region: '华东2', monthlyPrice: 2052, hourlyPrice: 2.85, totalStock: 50, remainingStock: 18, status: 'active' },
        { id: 202, skuCode: 'ECS-C4M8-HB2-002', productId: 2, region: '华北2', monthlyPrice: 2100, hourlyPrice: 2.92, totalStock: 30, remainingStock: 8, status: 'active' }
    ],
    3: [
        { id: 301, skuCode: 'ECS-C8M16-HB2-001', productId: 3, region: '华北2', monthlyPrice: 864, hourlyPrice: 1.20, totalStock: 60, remainingStock: 60, status: 'inactive' }
    ]
};

function getSkuByProductId(productId) {
    // 从全局SKU数据获取产品SKU
    return skuData[productId] || [];
}

function toggleSkuStatus(skuId, currentStatus) {
    const newStatus = currentStatus === 'active' ? 'inactive' : 'active';
    alert(`切换SKU ${skuId} 状态为: ${newStatus === 'active' ? '已上架' : '已下架'}`);
    // 实际项目中这里应该调用API更新状态并刷新列表
}

function editSkuPrice(skuId) {
    const sku = getSkuById(skuId);
    if (sku) {
        document.getElementById('editPriceSkuId').value = skuId;
        document.getElementById('monthlyPrice').value = sku.monthlyPrice;
        document.getElementById('hourlyPrice').value = sku.hourlyPrice;
        openModal('editPriceModal');
    }
}

function editSkuStock(skuId) {
    const sku = getSkuById(skuId);
    if (sku) {
        document.getElementById('editStockSkuId').value = skuId;
        document.getElementById('totalStock').value = sku.totalStock;
        openModal('editStockModal');
    }
}

function batchOperation(type) {
    document.getElementById('batchOperationType').value = type;
    const titleElement = document.getElementById('batchModalTitle');
    const messageElement = document.getElementById('batchConfirmMessage');
    const priceContainer = document.getElementById('batchPriceContainer');
    
    switch(type) {
        case 'online':
            titleElement.textContent = '批量上架确认';
            messageElement.textContent = '确定要将所有选中的SKU上架吗？';
            priceContainer.style.display = 'none';
            break;
        case 'offline':
            titleElement.textContent = '批量下架确认';
            messageElement.textContent = '确定要将所有选中的SKU下架吗？';
            priceContainer.style.display = 'none';
            break;
        case 'price':
            titleElement.textContent = '批量修改价格';
            messageElement.textContent = '请输入新价格，将应用到所有选中的SKU：';
            priceContainer.style.display = 'block';
            // 清空价格输入框
            document.getElementById('batchMonthlyPrice').value = '';
            document.getElementById('batchHourlyPrice').value = '';
            break;
    }
    openModal('batchConfirmModal');
}

function getSkuById(skuId) {
    // 获取所有SKU并查找指定ID的SKU
    const allSkus = [].concat(...Object.values(skuData));
    return allSkus.find(sku => sku.id === skuId);
}

function saveSkuPrice() {
    const skuId = parseInt(document.getElementById('editPriceSkuId').value);
    const monthlyPrice = parseFloat(document.getElementById('monthlyPrice').value);
    const hourlyPrice = parseFloat(document.getElementById('hourlyPrice').value);
    
    if (isNaN(monthlyPrice) || isNaN(hourlyPrice)) {
        alert('请输入有效的价格数值');
        return;
    }
    
    // 更新SKU价格
    const sku = getSkuById(skuId);
    if (sku) {
        sku.monthlyPrice = monthlyPrice;
        sku.hourlyPrice = hourlyPrice;
        renderSkuList(currentProductId); // 刷新SKU列表
        closeModal('editPriceModal');
        alert('价格修改成功');
    }
}

function saveSkuStock() {
    const skuId = parseInt(document.getElementById('editStockSkuId').value);
    const totalStock = parseInt(document.getElementById('totalStock').value);
    
    if (isNaN(totalStock) || totalStock < 0) {
        alert('请输入有效的总库存数值');
        return;
    }
    
    // 更新SKU库存
    const sku = getSkuById(skuId);
    if (sku) {
        // 如果新总库存小于当前剩余库存，自动调整剩余库存
        const newRemainingStock = Math.min(sku.remainingStock, totalStock);
        sku.totalStock = totalStock;
        sku.remainingStock = newRemainingStock;
        renderSkuList(currentProductId); // 刷新SKU列表
        closeModal('editStockModal');
        alert('库存修改成功');
    }
}

function confirmBatchOperation() {
    const type = document.getElementById('batchOperationType').value;
    // 在实际项目中，这里应该获取选中的SKU IDs
    // 这里简化处理，假设操作所有当前页面的SKU
    
    switch(type) {
        case 'online':
            // 批量上架逻辑
            updateSkusStatus(true);
            break;
        case 'offline':
            // 批量下架逻辑
            updateSkusStatus(false);
            break;
        case 'price':
            // 批量修改价格逻辑
            const monthlyPrice = parseFloat(document.getElementById('batchMonthlyPrice').value);
            const hourlyPrice = parseFloat(document.getElementById('batchHourlyPrice').value);
            if (isNaN(monthlyPrice) || isNaN(hourlyPrice)) {
                alert('请输入有效的价格数值');
                return;
            }
            updateSkusPrice(monthlyPrice, hourlyPrice);
            break;
    }
    closeModal('batchConfirmModal');
}

function updateSkusStatus(isActive) {
    // 更新当前产品所有SKU的状态
    const skus = getSkuByProductId(currentProductId);
    skus.forEach(sku => {
        sku.status = isActive ? 'active' : 'inactive';
    });
    renderSkuList(currentProductId);
    alert(`已${isActive ? '上架' : '下架'} ${skus.length}个SKU`);
}

function updateSkusPrice(monthlyPrice, hourlyPrice) {
    // 更新当前产品所有SKU的价格
    const skus = getSkuByProductId(currentProductId);
    skus.forEach(sku => {
        sku.monthlyPrice = monthlyPrice;
        sku.hourlyPrice = hourlyPrice;
    });
    renderSkuList(currentProductId);
    alert(`已更新 ${skus.length}个SKU的价格`);
}

function openModal(modalId) {
    document.getElementById(modalId).style.display = 'flex';
}

function closeModal(modalId) {
    document.getElementById(modalId).style.display = 'none';
}

function goBackToProductList() {
    document.querySelectorAll('.page').forEach(page => page.classList.remove('active'));
    document.getElementById('cloud-servers').classList.add('active');
}

// 全局变量存储当前产品ID
let currentProductId = null;

// 修改showProductDetail函数以设置当前产品ID
function showProductDetail(productId) {
    currentProductId = productId;
    // 隐藏所有页面
    document.querySelectorAll('.page').forEach(page => page.classList.remove('active'));
    
    // 显示产品详情页
    document.getElementById('product-detail').classList.add('active');
    
    // 模拟加载产品数据
    const product = getProductById(productId);
    if (product) {
        document.getElementById('productDetailTitle').textContent = product.name + ' - 详情';
        document.getElementById('detailProductName').textContent = product.name;
        document.getElementById('detailPackageSpec').textContent = product.spec;
        document.getElementById('detailArchitecture').textContent = product.architecture;
        document.getElementById('detailSpecType').textContent = product.type;
        document.getElementById('detailCpuConfig').textContent = product.cpu;
        document.getElementById('detailMemory').textContent = product.memory;
        document.getElementById('detailDescription').textContent = product.description;
        
        // 加载SKU列表
        renderSkuList(productId);
    }
}

// 计算产品管理功能
        function showAddServerModal() {
            document.getElementById('addServerModal').style.display = 'flex';
        }

        // 切换GPU配置显示/隐藏
        function toggleGpuConfig() {
            const gpuSupport = document.getElementById('gpuSupportSelect').value;
            const gpuConfigDetails = document.getElementById('gpuConfigDetails');

            if (gpuSupport === 'yes') {
                gpuConfigDetails.style.display = 'block';
            } else {
                gpuConfigDetails.style.display = 'none';
                // 清空GPU配置选项
                document.querySelector('select[name="gpuBrand"]').value = '';
                document.querySelector('select[name="gpuModel"]').value = '';
                document.querySelector('select[name="gpuMemory"]').value = '';
            }
        }

        function closeAddServerModal() {
            document.getElementById('addServerModal').style.display = 'none';
            document.getElementById('addServerForm').reset();

            // 重置数据中心选择
            document.querySelectorAll('input[name="regions"]').forEach(checkbox => {
                checkbox.checked = false;
            });
            document.getElementById('zoneCheckboxGroup').innerHTML =
                '<div class="empty-state">请先选择数据中心</div>';

            // 重置GPU配置显示状态
            document.getElementById('gpuConfigDetails').style.display = 'none';
        }



        function toggleServerStatus(id) {
            alert('切换云主机产品状态 ID: ' + id);
        }

        function deleteServerProduct(id) {
            if (confirm('确定要删除这个云主机产品吗？')) {
                alert('删除云主机产品 ID: ' + id);
            }
        }

        // 获取产品信息
        function getProductById(id) {
            // 模拟产品数据
            const products = {
                1: { id: 1, name: '通用均衡型G7', spec: 'G7.2B', cpu: '2核 Intel Xeon', memory: '4GB', gpu: '无GPU', region: '华东2', status: 'active' },
                2: { id: 2, name: 'GPU加速型GN7i', spec: 'GN7i.4L', cpu: '4核 Intel Xeon', memory: '16GB', gpu: 'NVIDIA T4', region: '华东2', status: 'active' },
                3: { id: 3, name: '计算优化型C9a', spec: 'C9a.2B', cpu: '8核 Intel Xeon', memory: '16GB', gpu: '无GPU', region: '华北2', status: 'inactive' }
            };
            return products[id] || null;
        }

        // 新增计算产品地域配置
        function editServerRegion(id) {
            // 获取产品信息
            const productInfo = getProductById(id);
            if (!productInfo) {
                alert('产品信息不存在');
                return;
            }

            // 设置产品ID
            document.getElementById('editRegionProductId').value = id;

            // 显示产品信息
            document.getElementById('editRegionProductInfo').innerHTML = `
                <div style="font-weight: 500; margin-bottom: 4px;">${productInfo.name}</div>
                <div style="color: #666; font-size: 14px;">${productInfo.spec} | ${productInfo.cpu} | ${productInfo.memory}</div>
            `;

            // 加载当前地域配置
            loadCurrentRegionConfig(id);

            // 显示模态框
            document.getElementById('editServerRegionModal').style.display = 'flex';
        }

        // 加载当前地域配置
        function loadCurrentRegionConfig(productId) {
            // 模拟当前产品的地域配置数据
            const currentConfig = {
                1: { regions: ['华东2'] },
                2: { regions: ['华东2'] },
                3: { regions: ['华北2'] }
            };

            const config = currentConfig[productId] || { regions: [], zones: [] };

            // 设置数据中心选择状态
            document.querySelectorAll('input[name="editRegions"]').forEach(checkbox => {
                checkbox.checked = config.regions.includes(checkbox.value);
            });


        }

        // 关闭新增地域模态框
        function closeEditServerRegionModal() {
            document.getElementById('editServerRegionModal').style.display = 'none';

            // 重置表单
            document.querySelectorAll('input[name="editRegions"]').forEach(checkbox => {
                checkbox.checked = false;
            });
        }

        // 保存地域配置
        function saveServerRegionConfig() {
            const productId = document.getElementById('editRegionProductId').value;
            const selectedRegions = Array.from(document.querySelectorAll('input[name="editRegions"]:checked'))
                .map(cb => cb.value);
            if (selectedRegions.length === 0) {
                alert('请至少选择一个数据中心');
                return;
            }

            // 这里可以发送到后端保存
            console.log('保存地域配置:', {
                productId: productId,
                regions: selectedRegions
            });

            alert(`地域配置保存成功！\n数据中心: ${selectedRegions.join(', ')}`);
            closeEditServerRegionModal();
        }



        }

        function addServerProduct() {
            const form = document.getElementById('addServerForm');

            // 验证数据中心选择
            const selectedRegions = Array.from(document.querySelectorAll('input[name="regions"]:checked'))
                .map(checkbox => checkbox.value);

            if (selectedRegions.length === 0) {
                alert('请至少选择一个数据中心');
                return;
            }

            // 收集表单数据
            const formData = new FormData(form);

            // 添加多选的地域和可用区数据
            formData.delete('regions');
            selectedRegions.forEach(region => formData.append('regions[]', region));

            // 显示选择的地域信息
            alert(`计算产品添加成功！\n\n数据中心: ${selectedRegions.join(', ')}`);

            closeAddServerModal();
        }
    </script>

    <!-- 新增计算产品弹窗 -->
    <div class="modal" id="addServerModal" style="display: none;">
        <div class="modal-content" style="width: 800px; max-width: 90vw;">
            <div class="modal-header">
                <h3>新增计算产品</h3>
                <button class="modal-close" onclick="closeAddServerModal()">&times;</button>
            </div>
            <div class="modal-body">
                <form id="addServerForm">
                    <div class="form-grid" style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                        <!-- 左列 -->
                        <div>
                            <div class="form-group">
                                <label>产品名称 *</label>
                                <input type="text" name="productName" required placeholder="如：通用均衡型G7">
                            </div>

                            <div class="form-group">
                                <label>套餐规格 *</label>
                                <input type="text" name="packageSpec" required placeholder="如：G7.2B">
                            </div>



                            <div class="form-group">
                                <label>产品描述 *</label>
                                <textarea name="description" rows="3" required placeholder="详细描述产品的适用场景和特性"></textarea>
                            </div>

                            <div class="form-group">
                                <label>CPU配置 *</label>
                                <div style="display: flex; gap: 10px;">
                                    <select name="cpuCores" required style="flex: 1;">
                                        <option value="">选择核数</option>
                                        <option value="1">1核</option>
                                        <option value="2">2核</option>
                                        <option value="4">4核</option>
                                        <option value="8">8核</option>
                                        <option value="16">16核</option>
                                        <option value="32">32核</option>
                                    </select>
                                    <select name="cpuType" required style="flex: 1;">
                                        <option value="">CPU类型</option>
                                        <option value="Intel Xeon">Intel Xeon</option>
                                        <option value="AMD EPYC">AMD EPYC</option>
                                        <option value="ARM">ARM</option>
                                    </select>
                                </div>
                            </div>

                            <div class="form-group">
                                <label>内存 *</label>
                                <select name="memory" required>
                                    <option value="">选择内存</option>
                                    <option value="1GB">1GB</option>
                                    <option value="2GB">2GB</option>
                                    <option value="4GB">4GB</option>
                                    <option value="8GB">8GB</option>
                                    <option value="16GB">16GB</option>
                                    <option value="32GB">32GB</option>
                                    <option value="64GB">64GB</option>
                                    <option value="128GB">128GB</option>
                                </select>
                            </div>


                        </div>

                        <!-- 右列 -->
                        <div>
                            <div class="form-group">
                                <label>GPU配置</label>
                                <select name="gpuSupport" id="gpuSupportSelect" onchange="toggleGpuConfig()" style="margin-bottom: 10px;">
                                    <option value="">是否支持GPU</option>
                                    <option value="yes">支持GPU</option>
                                    <option value="no">不支持GPU</option>
                                </select>
                                <div id="gpuConfigDetails" style="display: none;">
                                    <div style="display: flex; gap: 10px; margin-bottom: 10px;">
                                        <select name="gpuBrand" style="flex: 1;">
                                            <option value="">GPU品牌</option>
                                            <option value="NVIDIA">NVIDIA</option>
                                            <option value="AMD">AMD</option>
                                            <option value="Intel">Intel</option>
                                        </select>
                                        <select name="gpuModel" style="flex: 1;">
                                            <option value="">GPU型号</option>
                                            <option value="T4">T4</option>
                                            <option value="V100">V100</option>
                                            <option value="A100">A100</option>
                                            <option value="RTX 4090">RTX 4090</option>
                                        </select>
                                    </div>
                                    <select name="gpuMemory">
                                        <option value="">显存大小</option>
                                        <option value="4GB">4GB</option>
                                        <option value="8GB">8GB</option>
                                        <option value="16GB">16GB</option>
                                        <option value="24GB">24GB</option>
                                        <option value="32GB">32GB</option>
                                        <option value="80GB">80GB</option>
                                    </select>
                                </div>
                            </div>

                            <div class="form-group">
                                <label>数据中心配置 *</label>
                                <div class="multi-select-container">
                                    <div class="multi-select-title">选择数据中心（可多选）</div>
                                    <div class="checkbox-grid">
                                        <label class="checkbox-item">
                                            <input type="checkbox" name="regions" value="华东1">
                                            <span>华东1 (杭州)</span>
                                        </label>
                                        <label class="checkbox-item">
                                            <input type="checkbox" name="regions" value="华东2">
                                            <span>华东2 (上海)</span>
                                        </label>
                                        <label class="checkbox-item">
                                            <input type="checkbox" name="regions" value="华北1">
                                            <span>华北1 (青岛)</span>
                                        </label>
                                        <label class="checkbox-item">
                                            <input type="checkbox" name="regions" value="华北2">
                                            <span>华北2 (北京)</span>
                                        </label>
                                        <label class="checkbox-item">
                                            <input type="checkbox" name="regions" value="华南1">
                                            <span>华南1 (深圳)</span>
                                        </label>
                                    </div>
                                </div>
                            </div>



                            <div class="form-group">
                                <label>价格设置 *</label>
                                <div style="display: flex; gap: 10px;">
                                    <input type="number" name="hourlyPrice" step="0.01" required placeholder="小时价格" style="flex: 1;">
                                    <input type="number" name="monthlyPrice" step="0.01" required placeholder="月价格" style="flex: 1;">
                                </div>
                            </div>

                            <div class="form-group">
                                <label>产品状态</label>
                                <select name="status">
                                    <option value="active">上架</option>
                                    <option value="inactive">下架</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" onclick="closeAddServerModal()">取消</button>
                <button type="button" class="btn btn-primary" onclick="addServerProduct()">确认添加</button>
            </div>
        </div>
    </div>

    <!-- 编辑计算产品地域模态框 -->
    <div class="modal" id="editServerRegionModal" style="display: none;">
        <div class="modal-content" style="width: 600px;">
            <div class="modal-header">
                <h3 class="modal-title">新增产品地域配置</h3>
                <button class="modal-close" onclick="closeEditServerRegionModal()">&times;</button>
            </div>
            <div class="modal-body">
                <input type="hidden" id="editRegionProductId">

                <!-- 产品信息显示 -->
                <div class="form-group">
                    <label>产品信息</label>
                    <div id="editRegionProductInfo" style="padding: 12px; background: #f5f5f5; border-radius: 4px; margin-bottom: 16px;">
                        <!-- 产品信息将通过JavaScript填充 -->
                    </div>
                </div>

                <!-- 数据中心配置 -->
                <div class="form-group">
                    <label>数据中心配置 *</label>
                    <div class="multi-select-container">
                        <div class="multi-select-title">选择数据中心（可多选）</div>
                        <div id="editRegionCheckboxGroup" class="checkbox-grid">
                            <div class="checkbox-item">
                                <label>
                                    <input type="checkbox" name="editRegions" value="华东1">
                                    <span>华东1（杭州）</span>
                                </label>
                            </div>
                            <div class="checkbox-item">
                                <label>
                                    <input type="checkbox" name="editRegions" value="华东2">
                                    <span>华东2（上海）</span>
                                </label>
                            </div>
                            <div class="checkbox-item">
                                <label>
                                    <input type="checkbox" name="editRegions" value="华北1">
                                    <span>华北1（青岛）</span>
                                </label>
                            </div>
                            <div class="checkbox-item">
                                <label>
                                    <input type="checkbox" name="editRegions" value="华北2">
                                    <span>华北2（北京）</span>
                                </label>
                            </div>
                            <div class="checkbox-item">
                                <label>
                                    <input type="checkbox" name="editRegions" value="华南1">
                                    <span>华南1（深圳）</span>
                                </label>
                            </div>
                            <div class="checkbox-item">
                                <label>
                                    <input type="checkbox" name="editRegions" value="西南1">
                                    <span>西南1（成都）</span>
                                </label>
                            </div>
                        </div>
                    </div>
                </div>


            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" onclick="closeEditServerRegionModal()">取消</button>
                <button type="button" class="btn btn-primary" onclick="saveServerRegionConfig()">保存配置</button>
            </div>
        </div>
    </div>

    <!-- 云硬盘SKU修改价格模态框 -->
    <div class="modal" id="editDiskSkuPriceModal" style="display: none;">
        <div class="modal-content" style="width: 500px;">
            <div class="modal-header">
                <h3 class="modal-title">修改云硬盘SKU价格</h3>
                <button class="modal-close" onclick="closeModal('editDiskSkuPriceModal')">&times;</button>
            </div>
            <div class="modal-body">
                <input type="hidden" id="editDiskSkuPriceId">
                <div class="form-group">
                    <label class="form-label">SKU信息</label>
                    <div id="diskSkuPriceInfo" style="padding: 12px; background: #f5f5f5; border-radius: 6px; font-size: 14px; color: #666;">
                        <!-- SKU信息将动态填充 -->
                    </div>
                </div>
                <div class="form-group">
                    <label class="form-label">包年包月价格 (元/GB/月) <span style="color: #f5222d;">*</span></label>
                    <input type="number" class="form-input" id="editDiskMonthlyPrice" step="0.01" min="0" required>
                </div>
                <div class="form-group">
                    <label class="form-label">按量价格 (元/GB/时) <span style="color: #f5222d;">*</span></label>
                    <input type="number" class="form-input" id="editDiskHourlyPrice" step="0.0001" min="0" required>
                </div>
                <div style="font-size: 12px; color: #666; padding: 8px; background: #e6f7ff; border-radius: 4px; border-left: 3px solid #1890ff;">
                    💡 价格修改后将立即生效，影响该地域的所有新订单
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="closeModal('editDiskSkuPriceModal')">取消</button>
                <button class="btn btn-primary" onclick="saveDiskSkuPrice()">保存</button>
            </div>
        </div>
    </div>

    <!-- 云硬盘SKU修改库存模态框 -->
    <div class="modal" id="editDiskSkuStockModal" style="display: none;">
        <div class="modal-content" style="width: 500px;">
            <div class="modal-header">
                <h3 class="modal-title">修改云硬盘SKU库存</h3>
                <button class="modal-close" onclick="closeModal('editDiskSkuStockModal')">&times;</button>
            </div>
            <div class="modal-body">
                <input type="hidden" id="editDiskSkuStockId">
                <div class="form-group">
                    <label class="form-label">SKU信息</label>
                    <div id="diskSkuStockInfo" style="padding: 12px; background: #f5f5f5; border-radius: 6px; font-size: 14px; color: #666;">
                        <!-- SKU信息将动态填充 -->
                    </div>
                </div>
                <div class="form-group">
                    <label class="form-label">当前库存信息</label>
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px; padding: 12px; background: #f9f9f9; border-radius: 6px;">
                        <div>
                            <span style="font-size: 12px; color: #666;">总库存</span>
                            <div id="currentTotalStock" style="font-size: 16px; font-weight: 600; color: #333;"></div>
                        </div>
                        <div>
                            <span style="font-size: 12px; color: #666;">剩余库存</span>
                            <div id="currentRemainingStock" style="font-size: 16px; font-weight: 600; color: #333;"></div>
                        </div>
                    </div>
                </div>
                <div class="form-group">
                    <label class="form-label">新总库存 (GB) <span style="color: #f5222d;">*</span></label>
                    <input type="number" class="form-input" id="editDiskTotalStock" min="0" step="1" required>
                    <div style="font-size: 12px; color: #666; margin-top: 5px;">
                        注意：如果新总库存小于当前剩余库存，剩余库存将自动调整为新总库存值
                    </div>
                </div>
                <div style="font-size: 12px; color: #666; padding: 8px; background: #fff7e6; border-radius: 4px; border-left: 3px solid #faad14;">
                    ⚠️ 库存修改后将立即生效，请谨慎操作
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="closeModal('editDiskSkuStockModal')">取消</button>
                <button class="btn btn-primary" onclick="saveDiskSkuStock()">保存</button>
            </div>
        </div>
    </div>

<script>
// 显示新增云硬盘模态框
function showAddDiskModal() {
    document.getElementById('addDiskModal').style.display = 'flex';
}

// 关闭模态框
function closeModal(modalId) {
    document.getElementById(modalId).style.display = 'none';
}

// 保存新增云硬盘产品
function saveNewDiskProduct() {
    // 获取表单数据
    const productName = document.getElementById('diskProductName').value;
    const minCapacity = document.getElementById('minCapacity').value;
    const maxCapacity = document.getElementById('maxCapacity').value;
    const diskStep = document.getElementById('diskStep').value;
    const diskType = document.getElementById('diskType').value;
    const category = document.querySelector('input[name="category"]:checked').value;
    const monthlyPrice = document.getElementById('diskMonthlyPrice').value;
    const hourlyPrice = document.getElementById('diskHourlyPrice').value;
    // 获取选中的地域
    const regions = Array.from(document.querySelectorAll('input[name="diskRegion"]:checked')).map(cb => cb.value);

    const description = document.getElementById('diskDescription').value;
    const status = document.getElementById('diskStatus').value;

    // 验证必填字段
    if (!productName || !minCapacity || !maxCapacity || !diskStep || !diskType || !category || !monthlyPrice || !hourlyPrice) {
        alert('请填写所有必填字段');
        return;
    }

    // 验证容量范围
    if (parseInt(minCapacity) >= parseInt(maxCapacity)) {
        alert('容量范围最小值必须小于最大值');
        return;
    }

    // 验证价格
    if (parseFloat(monthlyPrice) <= 0 || parseFloat(hourlyPrice) <= 0) {
        alert('价格必须大于0');
        return;
    }

    // 验证地域
    if (regions.length === 0) {
        alert('请至少选择一个地域');
        return;
    }



    // 在实际应用中，这里会发送数据到后端保存
    // 这里仅做前端演示
    const categoryText = category === 'system' ? '系统盘' : '数据盘';
    const regionText = regions.join(', ');


    alert(`云硬盘产品保存成功！

产品信息：
• 产品名称: ${productName}
• 类别: ${categoryText}
• 云硬盘类型: ${diskType}
• 容量范围: ${minCapacity}-${maxCapacity}GB
• 步长: ${diskStep}GB

价格设置：
• 包年包月: ¥${parseFloat(monthlyPrice).toFixed(2)}/GB/月
• 按量计费: ¥${parseFloat(hourlyPrice).toFixed(4)}/GB/时

地域配置：
• 数据中心: ${regionText}
状态: ${status === 'active' ? '上架' : '下架'}`);

    // 关闭模态框并刷新页面
    closeModal('addDiskModal');
    location.reload();
}

// 云硬盘产品详情页功能
function showDiskProductDetail(productId) {
    // 隐藏所有页面
    document.querySelectorAll('.page').forEach(page => page.classList.remove('active'));

    // 显示云硬盘产品详情页
    document.getElementById('disk-detail').classList.add('active');

    // 模拟加载产品数据
    const product = getDiskProductById(productId);
    if (product) {
        document.getElementById('diskDetailTitle').textContent = product.name + ' - 详情';
        document.getElementById('diskDetailProductName').textContent = product.name;
        document.getElementById('diskDetailCapacityRange').textContent = product.capacityRange;
        document.getElementById('diskDetailStep').textContent = product.step;
        document.getElementById('diskDetailCategory').textContent = product.category;
        document.getElementById('diskDetailType').textContent = product.diskType;
        document.getElementById('diskDetailRegion').textContent = product.region;
        document.getElementById('diskDetailDescription').textContent = product.description;

        // 加载SKU列表
        renderDiskSkuList(productId);
    }
}

function goBackToDiskList() {
    // 隐藏详情页，显示云硬盘产品列表
    document.querySelectorAll('.page').forEach(page => page.classList.remove('active'));
    document.getElementById('cloud-disks').classList.add('active');
}

function getDiskProductById(id) {
    // 模拟云硬盘产品数据
    const diskProducts = [
        {
            id: 1,
            name: '高性能云硬盘SSD',
            category: '数据盘',
            diskType: 'SSD',
            capacityRange: '100-500GB',
            step: '100GB',
            region: '华东2（上海）',
            description: '适用于IO密集型应用，提供高性能和低延迟\n支持多种实例类型，满足不同业务需求\n数据可靠性高达99.999%'
        },
        {
            id: 2,
            name: '通用型云硬盘HDD',
            category: '系统盘',
            diskType: '普通',
            capacityRange: '200-1000GB',
            step: '50GB',
            region: '华北2（北京）',
            description: '性价比高，适用于大容量存储需求\n稳定可靠，数据持久性强\n支持动态扩容，无需停机'
        },
        {
            id: 3,
            name: '极速型云硬盘NVMe',
            category: '数据盘',
            diskType: '高效',
            capacityRange: '500-2000GB',
            step: '200GB',
            region: '华南1（深圳）',
            description: '超高性能云硬盘，适用于关键业务和数据库\n采用NVMe协议，IOPS高达100000\n低延迟设计，响应时间<1ms'
        }
    ];
    return diskProducts.find(p => p.id === id);
}

function renderDiskSkuList(productId) {
    const tbody = document.getElementById('diskSkuTableBody');
    tbody.innerHTML = '';

    // 模拟云硬盘SKU数据
    const diskSkus = getDiskSkuByProductId(productId);

    diskSkus.forEach(sku => {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td><span style="font-family: monospace; font-size: 12px; color: #666;">${sku.skuCode}</span></td>
            <td>${sku.region}</td>
            
            <td>¥${sku.monthlyPrice.toFixed(2)}/GB</td>
            <td>¥${sku.hourlyPrice.toFixed(4)}/GB/时</td>
            <td>${sku.totalStock}GB</td>
            <td>${sku.remainingStock}GB</td>
            <td><span class="status-badge status-${sku.status}">${sku.status === 'active' ? '已上架' : '已下架'}</span></td>
            <td>
                <div class="action-buttons">
                    <button class="btn-link" onclick="editDiskSkuPrice(${sku.id})">修改价格</button>
                    <button class="btn-link" onclick="editDiskSkuStock(${sku.id})">修改库存</button>
                    <button class="btn-link ${sku.status === 'active' ? 'danger' : ''}" onclick="toggleDiskSkuStatus(${sku.id}, '${sku.status}')">
                        ${sku.status === 'active' ? '下架' : '上架'}
                    </button>
                </div>
            </td>
        `;
        tbody.appendChild(row);
    });
}

// 全局云硬盘SKU数据
const diskSkuData = {
    1: [
        { id: 401, skuCode: 'EBS-SSD-HZ2-001', productId: 1, region: '华东2', monthlyPrice: 0.45, hourlyPrice: 0.0006, totalStock: 20000, remainingStock: 15600, status: 'active' },
        { id: 402, skuCode: 'EBS-SSD-HZ2-002', productId: 1, region: '华东2', monthlyPrice: 0.45, hourlyPrice: 0.0006, totalStock: 15000, remainingStock: 9800, status: 'active' },
        { id: 403, skuCode: 'EBS-SSD-HB2-003', productId: 1, region: '华北2', monthlyPrice: 0.47, hourlyPrice: 0.0007, totalStock: 10000, remainingStock: 6700, status: 'active' }
    ],
    2: [
        { id: 501, skuCode: 'EBS-HDD-HB2-001', productId: 2, region: '华北2', monthlyPrice: 0.30, hourlyPrice: 0.0004, totalStock: 30000, remainingStock: 24500, status: 'active' },
        { id: 502, skuCode: 'EBS-HDD-HB2-002', productId: 2, region: '华北2', monthlyPrice: 0.30, hourlyPrice: 0.0004, totalStock: 20000, remainingStock: 17800, status: 'active' },
        { id: 503, skuCode: 'EBS-HDD-HD1-003', productId: 2, region: '华东1', monthlyPrice: 0.32, hourlyPrice: 0.0004, totalStock: 25000, remainingStock: 22300, status: 'active' }
    ],
    3: [
        { id: 601, skuCode: 'EBS-NVME-HN1-001', productId: 3, region: '华南1', monthlyPrice: 0.90, hourlyPrice: 0.0013, totalStock: 8000, remainingStock: 8000, status: 'inactive' },
        { id: 602, skuCode: 'EBS-NVME-HN1-002', productId: 3, region: '华南1', monthlyPrice: 0.90, hourlyPrice: 0.0013, totalStock: 6000, remainingStock: 6000, status: 'inactive' }
    ]
};

function getDiskSkuByProductId(productId) {
    return diskSkuData[productId] || [];
}

function getDiskSkuById(skuId) {
    // 从所有产品的SKU中查找指定ID的SKU
    const allSkus = [].concat(...Object.values(diskSkuData));
    return allSkus.find(sku => sku.id === skuId);
}

function batchDiskOperation(type) {
    // 批量操作云硬盘SKU
    alert(`云硬盘批量${type === 'online' ? '上架' : type === 'offline' ? '下架' : '修改价格'}操作`);
}

function editDiskSkuPrice(skuId) {
    const sku = getDiskSkuById(skuId);
    if (!sku) {
        alert('SKU信息不存在');
        return;
    }

    // 填充SKU信息
    document.getElementById('editDiskSkuPriceId').value = skuId;
    document.getElementById('diskSkuPriceInfo').innerHTML = `
        <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 12px;">
            <div><strong>数据中心:</strong> ${sku.region}</div>
            
        </div>
    `;

    // 填充当前价格
    document.getElementById('editDiskMonthlyPrice').value = sku.monthlyPrice.toFixed(2);
    document.getElementById('editDiskHourlyPrice').value = sku.hourlyPrice.toFixed(4);

    // 显示模态框
    document.getElementById('editDiskSkuPriceModal').style.display = 'flex';
}

function editDiskSkuStock(skuId) {
    const sku = getDiskSkuById(skuId);
    if (!sku) {
        alert('SKU信息不存在');
        return;
    }

    // 填充SKU信息
    document.getElementById('editDiskSkuStockId').value = skuId;
    document.getElementById('diskSkuStockInfo').innerHTML = `
        <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 12px;">
            <div><strong>数据中心:</strong> ${sku.region}</div>
        </div>
    `;

    // 填充当前库存信息
    document.getElementById('currentTotalStock').textContent = `${sku.totalStock.toLocaleString()}GB`;
    document.getElementById('currentRemainingStock').textContent = `${sku.remainingStock.toLocaleString()}GB`;
    document.getElementById('editDiskTotalStock').value = sku.totalStock;

    // 显示模态框
    document.getElementById('editDiskSkuStockModal').style.display = 'flex';
}

function saveDiskSkuPrice() {
    const skuId = parseInt(document.getElementById('editDiskSkuPriceId').value);
    const monthlyPrice = parseFloat(document.getElementById('editDiskMonthlyPrice').value);
    const hourlyPrice = parseFloat(document.getElementById('editDiskHourlyPrice').value);

    // 验证输入
    if (!monthlyPrice || monthlyPrice <= 0) {
        alert('请输入有效的包年包月价格');
        return;
    }

    if (!hourlyPrice || hourlyPrice <= 0) {
        alert('请输入有效的按量价格');
        return;
    }

    // 查找并更新SKU价格
    const sku = getDiskSkuById(skuId);
    if (sku) {
        const oldMonthlyPrice = sku.monthlyPrice;
        const oldHourlyPrice = sku.hourlyPrice;

        sku.monthlyPrice = monthlyPrice;
        sku.hourlyPrice = hourlyPrice;

        // 在实际应用中，这里会发送数据到后端保存
        alert(`云硬盘SKU价格修改成功！

SKU信息: ${sku.region}

价格变更:
• 包年包月: ¥${oldMonthlyPrice.toFixed(2)}/GB/月 → ¥${monthlyPrice.toFixed(2)}/GB/月
• 按量计费: ¥${oldHourlyPrice.toFixed(4)}/GB/时 → ¥${hourlyPrice.toFixed(4)}/GB/时

新价格已生效！`);

        // 关闭模态框并刷新SKU列表
        closeModal('editDiskSkuPriceModal');

        // 重新渲染当前产品的SKU列表
        const currentProductId = getCurrentProductId();
        if (currentProductId) {
            renderDiskSkuList(currentProductId);
        }
    }
}

function saveDiskSkuStock() {
    const skuId = parseInt(document.getElementById('editDiskSkuStockId').value);
    const newTotalStock = parseInt(document.getElementById('editDiskTotalStock').value);

    // 验证输入
    if (!newTotalStock || newTotalStock < 0) {
        alert('请输入有效的总库存数量');
        return;
    }

    // 查找并更新SKU库存
    const sku = getDiskSkuById(skuId);
    if (sku) {
        const oldTotalStock = sku.totalStock;
        const oldRemainingStock = sku.remainingStock;

        sku.totalStock = newTotalStock;

        // 如果新总库存小于当前剩余库存，调整剩余库存
        if (newTotalStock < sku.remainingStock) {
            sku.remainingStock = newTotalStock;
        }

        // 在实际应用中，这里会发送数据到后端保存
        alert(`云硬盘SKU库存修改成功！

SKU信息: ${sku.region}

库存变更:
• 总库存: ${oldTotalStock.toLocaleString()}GB → ${newTotalStock.toLocaleString()}GB
• 剩余库存: ${oldRemainingStock.toLocaleString()}GB → ${sku.remainingStock.toLocaleString()}GB

新库存已生效！`);

        // 关闭模态框并刷新SKU列表
        closeModal('editDiskSkuStockModal');

        // 重新渲染当前产品的SKU列表
        const currentProductId = getCurrentProductId();
        if (currentProductId) {
            renderDiskSkuList(currentProductId);
        }
    }
}

function getCurrentProductId() {
    // 从页面标题中提取产品ID，这里简化处理
    // 在实际应用中，可以通过全局变量或其他方式获取当前产品ID
    const titleElement = document.getElementById('diskDetailTitle');
    if (titleElement) {
        const titleText = titleElement.textContent;
        if (titleText.includes('高性能云硬盘SSD')) return 1;
        if (titleText.includes('通用型云硬盘HDD')) return 2;
        if (titleText.includes('极速型云硬盘NVMe')) return 3;
    }
    return null;
}

function toggleDiskSkuStatus(skuId, currentStatus) {
    const newStatus = currentStatus === 'active' ? 'inactive' : 'active';
    const sku = getDiskSkuById(skuId);

    if (sku) {
        sku.status = newStatus;

        alert(`云硬盘SKU状态切换成功！

SKU信息: ${sku.region}
新状态: ${newStatus === 'active' ? '已上架' : '已下架'}`);

        // 重新渲染当前产品的SKU列表
        const currentProductId = getCurrentProductId();
        if (currentProductId) {
            renderDiskSkuList(currentProductId);
        }
    }
}

// 云硬盘详情、编辑、状态切换和删除函数（实际应用中需要实现）
// 产品数据
const productData = {
    1: {
        id: 1,
        name: '高性能云硬盘SSD',
        category: '数据盘',
        diskType: 'SSD',
        capacityRange: '100-500GB',
        step: '100GB',
        region: '华东2（上海）',
        description: '适用于IO密集型应用，提供高性能和低延迟\n支持多种实例类型，满足不同业务需求\n数据可靠性高达99.999%',
        status: 'active'
    },
    2: {
        id: 2,
        name: '通用型云硬盘HDD',
        category: '系统盘',
        diskType: '普通',
        capacityRange: '200-1000GB',
        step: '50GB',
        region: '华北2（北京）',
        description: '性价比高，适用于大容量存储需求\n稳定可靠，数据持久性强\n支持动态扩容，无需停机',
        status: 'active'
    },
    3: {
        id: 3,
        name: '极速型云硬盘NVMe',
        category: '数据盘',
        diskType: '高效',
        capacityRange: '500-2000GB',
        step: '200GB',
        region: '华南1（深圳）',
        description: '超高性能云硬盘，适用于关键业务和数据库\n采用NVMe协议，IOPS高达100000\n低延迟设计，响应时间<1ms',
        status: 'inactive'
    }
};

function showDiskDetail(id) {
    const product = productData[id];
    if (product) {
        document.getElementById('detailProductId').textContent = product.id;
        document.getElementById('detailTitle').textContent = product.name;
        document.getElementById('detailProductName').textContent = product.name;
        document.getElementById('detailCategory').textContent = product.category;
        document.getElementById('detailDiskType').textContent = product.diskType;
        document.getElementById('detailCapacityRange').textContent = product.capacityRange;
        document.getElementById('detailDiskStep').textContent = product.step;
        document.getElementById('detailRegion').textContent = product.region;
        document.getElementById('detailDescription').textContent = product.description;

        const statusToggleBtn = document.getElementById('detailStatusToggleBtn');
        if (product.status === 'active') {
            statusToggleBtn.textContent = '下架';
        } else {
            statusToggleBtn.textContent = '上架';
        }

        // 状态切换按钮事件
        statusToggleBtn.onclick = function() {
            if (confirm(`确定要${product.status === 'active' ? '下架' : '上架'}产品"${product.name}"吗？`)) {
                // 更新产品状态
                product.status = product.status === 'active' ? 'inactive' : 'active';
                statusToggleBtn.textContent = product.status === 'active' ? '下架' : '上架';
                alert(`产品"${product.name}"已${product.status === 'active' ? '上架' : '下架'}`);
                
                // 更新列表中的状态
                const statusBadge = document.querySelector(`#diskProductsTable tr:nth-child(${id}) .status-badge`);
                if (statusBadge) {
                    statusBadge.className = `status-badge status-${product.status}`;
                    statusBadge.textContent = product.status === 'active' ? '上架' : '下架';
                }
            }
        };

        // 显示详情模态框
        document.getElementById('diskDetailModal').style.display = 'block';
    }
}

function editDiskProduct(id) {
    alert('编辑云硬盘产品，ID: ' + id);
}

function toggleDiskStatus(id) {
    alert('切换云硬盘状态，ID: ' + id);
}

function deleteDiskProduct(id) {
    if (confirm('确定要删除该云硬盘产品吗？')) {
        alert('删除云硬盘产品，ID: ' + id);
    }
}
</script>
</body>
</html>
