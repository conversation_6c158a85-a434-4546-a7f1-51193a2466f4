<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>菜单测试</title>
    <style>
        .sidebar {
            width: 200px;
            background-color: #f0f0f0;
            padding: 10px;
            z-index: 100;
        }
        .menu-item {
            padding: 10px;
            cursor: pointer;
            border: 1px solid red;
        }
        .menu-item.active {
            background-color: #ccc;
        }
        .submenu {
            display: none;
            padding-left: 20px;
        }
        .menu-item.expanded .submenu {
            display: block;
        }
        .submenu-item {
            padding: 5px;
            cursor: pointer;
            border: 1px solid red;
        }
        .page {
            display: none;
            margin-left: 220px;
            padding: 20px;
        }
        .page.active {
            display: block;
        }
    </style>
</head>
<body>
    <div class="sidebar">
        <div class="menu-item" data-page="order">订单管理</div>
        <div class="menu-item has-submenu">
            产品管理
            <div class="submenu">
                <div class="submenu-item" data-page="compute">计算</div>
                <div class="submenu-item" data-page="disk">云硬盘</div>
            </div>
        </div>
    </div>

    <div id="order" class="page active">订单管理页面</div>
    <div id="compute" class="page">计算产品页面</div>
    <div id="disk" class="page">云硬盘页面</div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM已加载完成，开始初始化菜单');

            // 一级菜单点击事件
            document.querySelectorAll('.menu-item[data-page]').forEach(item => {
                item.addEventListener('click', function(event) {
                    event.stopPropagation();
                    switchPage(this.dataset.page, event);
                });
                console.log('绑定一级菜单点击事件:', item.textContent.trim());
            });

            // 子菜单点击事件
            document.querySelectorAll('.submenu-item[data-page]').forEach(item => {
                item.addEventListener('click', function(event) {
                    event.stopPropagation();
                    switchPage(this.dataset.page, event);
                });
                console.log('绑定子菜单点击事件:', item.textContent.trim());
            });

            // 产品管理子菜单展开/折叠
            const productMenu = document.querySelector('.menu-item.has-submenu');
            if (productMenu) {
                productMenu.addEventListener('click', function(event) {
                    event.stopPropagation();
                    this.classList.toggle('expanded');
                    console.log('产品管理菜单展开状态:', this.classList.contains('expanded'));
                });
            }

            // 切换页面显示函数
            function switchPage(pageId, event) {
                // 隐藏所有页面
                document.querySelectorAll('.page').forEach(page => {
                    page.classList.remove('active');
                });

                // 显示选中页面
                const targetPage = document.getElementById(pageId);
                if (targetPage) {
                    targetPage.classList.add('active');
                    console.log('显示页面:', pageId);
                } else {
                    console.error('未找到页面:', pageId);
                }

                // 更新导航菜单项激活状态
                document.querySelectorAll('.menu-item, .submenu-item').forEach(item => {
                    item.classList.remove('active');
                });

                // 激活当前点击的菜单项
                if (event && event.currentTarget) {
                    event.currentTarget.classList.add('active');
                    console.log('激活菜单项:', event.currentTarget.textContent.trim());
                }
            }
        });
    </script>
</body>
</html>