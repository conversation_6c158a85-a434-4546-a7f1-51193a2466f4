<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>菜单测试</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        body {
            font-family: Arial, sans-serif;
            display: flex;
            height: 100vh;
        }
        .sidebar {
            width: 200px;
            background-color: #1f2937;
            color: white;
            z-index: 100;
        }
        .menu-item {
            padding: 15px 20px;
            cursor: pointer;
            transition: background-color 0.3s;
            position: relative;
        }
        .menu-item:hover {
            background-color: #374151;
        }
        .menu-item.has-submenu::after {
            content: '▼';
            position: absolute;
            right: 15px;
            font-size: 10px;
        }
        .menu-item.has-submenu.expanded::after {
            content: '▲';
        }
        .submenu {
            background-color: #374151;
            display: none;
        }
        .menu-item.has-submenu.expanded .submenu {
            display: block;
        }
        .submenu-item {
            padding: 10px 30px;
            cursor: pointer;
            transition: background-color 0.3s;
        }
        .submenu-item:hover {
            background-color: #4b5563;
        }
        .content {
            flex: 1;
            padding: 20px;
        }
        .page {
            display: none;
        }
        .page.active {
            display: block;
        }
    </style>
</head>
<body>
    <div class="sidebar">
        <div class="menu-item" onclick="switchPage('dashboard')">控制台</div>
        <div class="menu-item has-submenu" onclick="toggleSubmenu(this)">
            订单管理
            <div class="submenu">
                <div class="submenu-item" onclick="switchPage('orders')">订单列表</div>
                <div class="submenu-item" onclick="switchPage('order-detail')">订单详情</div>
            </div>
        </div>
        <div class="menu-item has-submenu" onclick="toggleSubmenu(this)">
            产品管理
            <div class="submenu">
                <div class="submenu-item" onclick="switchPage('cloud-servers')">计算产品</div>
                <div class="submenu-item" onclick="switchPage('cloud-disks')">云硬盘</div>
            </div>
        </div>
        <div class="menu-item" onclick="switchPage('customers')">客户管理</div>
        <div class="menu-item" onclick="switchPage('reports')">报表统计</div>
    </div>

    <div class="content">
        <div class="page active" id="dashboard">控制台页面</div>
        <div class="page" id="orders">订单列表页面</div>
        <div class="page" id="order-detail">订单详情页面</div>
        <div class="page" id="cloud-servers">计算产品页面</div>
        <div class="page" id="cloud-disks">云硬盘页面</div>
        <div class="page" id="customers">客户管理页面</div>
        <div class="page" id="reports">报表统计页面</div>
    </div>

    <script>
        // 菜单交互逻辑
        function toggleSubmenu(menuItem) {
            menuItem.classList.toggle('expanded');
        }

        function switchPage(pageId) {
            // 隐藏所有页面
            document.querySelectorAll('.page').forEach(page => {
                page.classList.remove('active');
            });

            // 显示选中页面
            const selectedPage = document.getElementById(pageId);
            if (selectedPage) {
                selectedPage.classList.add('active');
            }

            // 打印调试信息
            console.log('切换到页面:', pageId);
        }

        // 初始化菜单事件
        document.addEventListener('DOMContentLoaded', function() {
            console.log('页面加载完成，菜单初始化');
        });
    </script>
</body>
</html>