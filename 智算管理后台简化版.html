<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智算管理后台简化版</title>
    <style>
        /* 基础样式重置 */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Arial', sans-serif;
        }

        body {
            display: flex;
            min-height: 100vh;
            background-color: #f5f7fa;
        }

        /* 侧边栏样式 */
        .sidebar {
            width: 220px;
            background-color: #2c3e50;
            color: white;
            padding: 20px 0;
            z-index: 100;
        }

        .menu-item {
            padding: 12px 20px;
            cursor: pointer;
            transition: background-color 0.3s;
        }

        .menu-item:hover {
            background-color: #34495e;
        }

        .menu-item.has-submenu {
            position: relative;
        }

        .menu-item.has-submenu::after {
            content: '>';
            position: absolute;
            right: 20px;
            transition: transform 0.3s;
        }

        .menu-item.has-submenu.expanded::after {
            transform: rotate(90deg);
        }

        .submenu {
            display: none;
            background-color: #34495e;
            padding-left: 20px;
        }

        .menu-item.has-submenu.expanded .submenu {
            display: block;
        }

        .submenu-item {
            padding: 10px 0;
            cursor: pointer;
        }

        .submenu-item:hover {
            color: #3498db;
        }

        /* 主内容区样式 */
        .main-content {
            flex: 1;
            padding: 20px;
        }

        .page {
            display: none;
            background-color: white;
            padding: 20px;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }

        .page.active {
            display: block;
        }
    </style>
</head>
<body>
    <div class="sidebar">
        <div class="menu-item" data-page="dashboard">仪表盘</div>
        <div class="menu-item has-submenu">
            产品管理
            <div class="submenu">
                <div class="submenu-item" data-page="compute">计算产品</div>
                <div class="submenu-item" data-page="storage">存储产品</div>
                <div class="submenu-item" data-page="network">网络产品</div>
            </div>
        </div>
        <div class="menu-item" data-page="orders">订单管理</div>
        <div class="menu-item" data-page="users">用户管理</div>
        <div class="menu-item" data-page="settings">系统设置</div>
    </div>

    <div class="main-content">
        <div id="dashboard" class="page active">
            <h1>欢迎来到仪表盘</h1>
            <p>这是简化版的管理后台仪表盘。</p>
        </div>
        <div id="compute" class="page">
            <h1>计算产品管理</h1>
            <p>管理您的计算资源。</p>
        </div>
        <div id="storage" class="page">
            <h1>存储产品管理</h1>
            <p>管理您的存储资源。</p>
        </div>
        <div id="network" class="page">
            <h1>网络产品管理</h1>
            <p>管理您的网络资源。</p>
        </div>
        <div id="orders" class="page">
            <h1>订单管理</h1>
            <p>查看和管理您的订单。</p>
        </div>
        <div id="users" class="page">
            <h1>用户管理</h1>
            <p>管理系统用户。</p>
        </div>
        <div id="settings" class="page">
            <h1>系统设置</h1>
            <p>配置系统参数。</p>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 菜单展开/折叠功能
            const submenuItems = document.querySelectorAll('.menu-item.has-submenu');
            submenuItems.forEach(item => {
                item.addEventListener('click', function(e) {
                    // 阻止事件冒泡，避免触发页面切换
                    e.stopPropagation();
                    this.classList.toggle('expanded');
                });
            });

            // 页面切换功能
            const menuItems = document.querySelectorAll('.menu-item[data-page]');
            const submenuPageItems = document.querySelectorAll('.submenu-item[data-page]');

            function switchPage(pageId) {
                // 隐藏所有页面
                document.querySelectorAll('.page').forEach(page => {
                    page.classList.remove('active');
                });
                // 显示选中页面
                const targetPage = document.getElementById(pageId);
                if (targetPage) {
                    targetPage.classList.add('active');
                } else {
                    console.error(`未找到页面: ${pageId}`);
                }
            }

            // 绑定一级菜单点击事件
            menuItems.forEach(item => {
                item.addEventListener('click', function() {
                    const pageId = this.getAttribute('data-page');
                    switchPage(pageId);
                });
            });

            // 绑定子菜单点击事件
            submenuPageItems.forEach(item => {
                item.addEventListener('click', function() {
                    const pageId = this.getAttribute('data-page');
                    switchPage(pageId);
                });
            });
        });
    </script>
</body>
</html>