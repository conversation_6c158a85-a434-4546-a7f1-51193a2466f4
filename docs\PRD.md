# 云主机产品管理系统 - 产品需求文档

## 1. 文档信息
- **版本**: 1.0
- **日期**: 2023-11-15
- **状态**: 初稿
- **编写人**: AI助手

## 2. 产品概述
### 2.1 产品目标
开发一套云主机产品管理系统，支持管理员对云主机产品进行创建、查询、编辑和管理SKU库存与价格，提供直观的产品列表和详情页展示。

### 2.2 用户角色
- **系统管理员**: 负责产品信息维护、库存管理、价格调整等操作

### 2.3 核心价值
- 提供清晰的产品层级结构展示（产品->SKU）
- 支持灵活的库存和价格管理
- 提供批量操作功能提高管理效率

## 3. 功能需求
### 3.1 云主机产品列表页
#### 3.1.1 产品展示
| 字段 | 说明 | 数据类型 |
|------|------|----------|
| 产品名称 | 云主机产品名称，如"通用均衡型G7" | 字符串 |
| 规格 | 产品规格型号，如"G7.2B" | 字符串 |
| 架构 | 处理器架构，如"x86_64" | 字符串 |
| 类型 | 产品分类，如"通用均衡型" | 字符串 |
| 操作 | 包含查看详情按钮 | - |

#### 3.1.2 操作功能
- 点击产品名称可进入产品详情页
- 支持新增产品（通过模态框）

### 3.2 产品详情页
#### 3.2.1 产品基本信息
| 字段 | 说明 | 数据类型 |
|------|------|----------|
| 产品名称 | 云主机产品名称 | 字符串 |
| 规格 | 产品规格型号 | 字符串 |
| 架构 | 处理器架构 | 字符串 |
| 类型 | 产品分类 | 字符串 |
| CPU配置 | CPU核心数及型号 | 字符串 |
| 内存 | 内存容量 | 字符串 |
| 描述 | 产品详细说明 | 文本 |

#### 3.2.2 SKU列表管理
| 字段 | 说明 | 数据类型 |
|------|------|----------|
| 数据中心 | 如"华东2" | 字符串 |
| 可用区 | 如"可用区A" | 字符串 |
| 包年包月价格 | 月度价格（元） | 数字 |
| 按量价格 | 小时价格（元） | 数字 |
| 总库存 | 该SKU总库存数量 | 整数 |
| 剩余库存 | 可售库存数量 | 整数 |
| 状态 | 上架/下架状态 | 枚举 |
| 操作 | 修改价格、修改库存、状态切换按钮 | - |

#### 3.2.3 批量操作功能
- 批量上架：将选中SKU状态改为上架
- 批量下架：将选中SKU状态改为下架
- 批量修改价格：统一设置选中SKU的价格

### 3.3 库存管理规则
- 仅允许修改总库存，剩余库存根据总库存自动调整
- 当总库存减少且小于当前剩余库存时，剩余库存自动调整为新的总库存值
- 库存值必须为非负整数

### 3.4 价格管理规则
- 支持单独修改SKU价格或批量修改价格
- 价格支持两位小数精度
- 价格值必须为非负数

## 4. 界面原型说明
### 4.1 产品列表页
- 采用表格布局展示产品列表
- 顶部有新增产品按钮
- 每行产品名称可点击进入详情页

### 4.2 产品详情页
- 顶部显示产品基本信息，采用卡片式布局
- 中部为批量操作按钮区域
- 下部为SKU列表，采用表格展示
- 操作按钮悬浮在表格行末尾

### 4.3 模态框设计
- 价格修改模态框：包含包年包月价格和按量价格输入框
- 库存修改模态框：仅包含总库存输入框
- 批量操作模态框：根据操作类型显示不同内容

## 5. 数据结构定义
### 5.1 产品数据结构
```json
{
  "id": 1,
  "name": "通用均衡型G7",
  "spec": "G7.2B",
  "architecture": "x86_64",
  "type": "通用均衡型",
  "cpu": "2核 Intel Xeon",
  "memory": "4GB",
  "description": "适用于中小型Web应用、轻量级数据库、开发测试环境"
}
```

### 5.2 SKU数据结构
```json
{
  "id": 101,
  "productId": 1,
  "region": "华东2",
  "zone": "可用区A",
  "monthlyPrice": 324,
  "hourlyPrice": 0.45,
  "totalStock": 100,
  "remainingStock": 78,
  "status": "active"
}
```

## 6. 业务规则
1. 剩余库存 ≤ 总库存
2. 价格修改后实时生效
3. SKU状态变更需记录操作日志
4. 批量操作时至少选择一个SKU
5. 所有数值输入需进行合法性校验

## 7. 非功能需求
### 7.1 性能要求
- 页面加载时间 < 2秒
- 数据操作响应时间 < 1秒

### 7.2 兼容性要求
- 支持Chrome、Firefox、Edge最新版本
- 适配1366×768及以上分辨率

### 7.3 安全要求
- 所有操作需验证用户权限
- 敏感操作需记录审计日志

## 8. 验收标准
1. 产品列表页正确展示所有产品信息
2. 点击产品名称可正确跳转至详情页
3. 详情页正确展示产品基本信息和SKU列表
4. 价格修改功能可正常保存并刷新展示
5. 库存修改功能仅允许修改总库存，剩余库存自动调整
6. 批量操作功能可正常对选中SKU生效
7. 所有输入框有正确的合法性校验
